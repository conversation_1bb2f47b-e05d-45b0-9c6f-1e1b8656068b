/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Code generated by "plc4xGenerator -type=NetworkAdapter -prefix=netservice_"; DO NOT EDIT.

package netservice

import (
	"context"
	"encoding/binary"
	"fmt"
	"github.com/apache/plc4x/plc4go/spi/utils"
)

var _ = fmt.Printf

func (d *NetworkAdapter) Serialize() ([]byte, error) {
	if d == nil {
		return nil, fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	wb := utils.NewWriteBufferByteBased(utils.WithByteOrderForByteBasedBuffer(binary.BigEndian))
	if err := d.SerializeWithWriteBuffer(context.Background(), wb); err != nil {
		return nil, err
	}
	return wb.GetBytes(), nil
}

func (d *NetworkAdapter) SerializeWithWriteBuffer(ctx context.Context, writeBuffer utils.WriteBuffer) error {
	if d == nil {
		return fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	if err := writeBuffer.PushContext("NetworkAdapter"); err != nil {
		return err
	}
	if err := d.ClientContract.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}

	if err := writeBuffer.WriteString("adapterSAP", uint32(len(fmt.Sprintf("%p", d.adapterSAP))*8), fmt.Sprintf("%p", d.adapterSAP)); err != nil {
		return err
	}
	if d.adapterNet != nil {
		if err := writeBuffer.WriteUint16("adapterNet", 16, *d.adapterNet); err != nil {
			return err
		}
	}
	if d.adapterAddr != nil {
		{
			_value := fmt.Sprintf("%v", d.adapterAddr)

			if err := writeBuffer.WriteString("adapterAddr", uint32(len(_value)*8), _value); err != nil {
				return err
			}
		}
	}
	if d.adapterNetConfigured != nil {
		if err := writeBuffer.WriteInt64("adapterNetConfigured", 64, int64(*d.adapterNetConfigured)); err != nil {
			return err
		}
	}
	if err := writeBuffer.PopContext("NetworkAdapter"); err != nil {
		return err
	}
	return nil
}

func (d *NetworkAdapter) String() string {
	if alternateStringer, ok := any(d).(utils.AlternateStringer); ok {
		if alternateString, use := alternateStringer.AlternateString(); use {
			return alternateString
		}
	}
	wb := utils.NewWriteBufferBoxBased(utils.WithWriteBufferBoxBasedMergeSingleBoxes(), utils.WithWriteBufferBoxBasedOmitEmptyBoxes())
	if err := wb.WriteSerializable(context.Background(), d); err != nil {
		return err.Error()
	}
	return wb.GetBox().String()
}
