/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Code generated by "plc4xGenerator -type=IOCB -prefix=iocb_"; DO NOT EDIT.

package iocb

import (
	"context"
	"encoding/binary"
	"fmt"
	"github.com/apache/plc4x/plc4go/spi/utils"
)

var _ = fmt.Printf

func (d *IOCB) Serialize() ([]byte, error) {
	if d == nil {
		return nil, fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	wb := utils.NewWriteBufferByteBased(utils.WithByteOrderForByteBasedBuffer(binary.BigEndian))
	if err := d.SerializeWithWriteBuffer(context.Background(), wb); err != nil {
		return nil, err
	}
	return wb.GetBytes(), nil
}

func (d *IOCB) SerializeWithWriteBuffer(ctx context.Context, writeBuffer utils.WriteBuffer) error {
	if d == nil {
		return fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	if err := writeBuffer.PushContext("IOCB"); err != nil {
		return err
	}

	if err := writeBuffer.WriteInt64("ioID", 64, int64(d.ioID)); err != nil {
		return err
	}

	if err := writeBuffer.WriteString("request", uint32(len(d.request.String())*8), d.request.String()); err != nil {
		return err
	}
	if d.destination != nil {
		if err := writeBuffer.WriteString("destination", uint32(len(d.destination.String())*8), d.destination.String()); err != nil {
			return err
		}
	}

	if err := writeBuffer.WriteString("ioState", uint32(len(d.ioState.String())*8), d.ioState.String()); err != nil {
		return err
	}

	if err := writeBuffer.WriteString("ioResponse", uint32(len(d.ioResponse.String())*8), d.ioResponse.String()); err != nil {
		return err
	}

	if d.ioError != nil {
		_errString := d.ioError.Error()
		if err := writeBuffer.WriteString("ioError", uint32(len(_errString)*8), _errString); err != nil {
			return err
		}
	}

	if err := writeBuffer.WriteBit("ioCompleteDone", d.ioCompleteDone); err != nil {
		return err
	}
	if err := writeBuffer.PushContext("ioQueue", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	for _, elem := range d.ioQueue {
		_value := fmt.Sprintf("%v", elem)

		if err := writeBuffer.WriteString("ioQueue", uint32(len(_value)*8), _value); err != nil {
			return err
		}
	}
	if err := writeBuffer.PopContext("ioQueue", utils.WithRenderAsList(true)); err != nil {
		return err
	}

	if d.ioTimeout != nil {
		if serializableField, ok := any(d.ioTimeout).(utils.Serializable); ok {
			if err := writeBuffer.PushContext("ioTimeout"); err != nil {
				return err
			}
			if err := serializableField.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
				return err
			}
			if err := writeBuffer.PopContext("ioTimeout"); err != nil {
				return err
			}
		} else {
			stringValue := fmt.Sprintf("%v", d.ioTimeout)
			if err := writeBuffer.WriteString("ioTimeout", uint32(len(stringValue)*8), stringValue); err != nil {
				return err
			}
		}
	}

	_ioTimoutCancel_plx4gen_description := fmt.Sprintf("%d element(s)", len(d.ioTimoutCancel))
	if err := writeBuffer.WriteString("ioTimoutCancel", uint32(len(_ioTimoutCancel_plx4gen_description)*8), _ioTimoutCancel_plx4gen_description); err != nil {
		return err
	}

	if err := writeBuffer.WriteInt64("priority", 64, int64(d.priority)); err != nil {
		return err
	}
	if err := writeBuffer.PopContext("IOCB"); err != nil {
		return err
	}
	return nil
}

func (d *IOCB) String() string {
	if alternateStringer, ok := any(d).(utils.AlternateStringer); ok {
		if alternateString, use := alternateStringer.AlternateString(); use {
			return alternateString
		}
	}
	wb := utils.NewWriteBufferBoxBased(utils.WithWriteBufferBoxBasedMergeSingleBoxes(), utils.WithWriteBufferBoxBasedOmitEmptyBoxes())
	if err := wb.WriteSerializable(context.Background(), d); err != nil {
		return err.Error()
	}
	return wb.GetBox().String()
}
