/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package constructeddata

import (
	. "github.com/apache/plc4x/plc4go/internal/bacnetip/bacgopes/comp"
)

type IsAnyAtomic interface {
	isAnyAtomic() bool
}

// TODO: finish me
type AnyAtomic struct {
	value any
}

var _ IsAnyAtomic = (*AnyAtomic)(nil)

func NewAnyAtomic(args Args) (*AnyAtomic, error) {
	a := &AnyAtomic{}
	return a, nil
}

func (a *AnyAtomic) isAnyAtomic() bool {
	return true
}

func (a *AnyAtomic) GetValue() any {
	return a.value
}

func (a *AnyAtomic) Encode(arg Arg) {}

func (a *AnyAtomic) Decode(arg Arg) error {
	return nil
}
