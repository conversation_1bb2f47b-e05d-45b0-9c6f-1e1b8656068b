/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package basetypes

import (
	"github.com/pkg/errors"

	. "github.com/apache/plc4x/plc4go/internal/bacnetip/bacgopes/comp"
	. "github.com/apache/plc4x/plc4go/internal/bacnetip/bacgopes/primitivedata"
)

type PropertyIdentifier struct {
	*Enumerated
	vendorRange  vendorRange
	enumerations map[string]uint64
}

func NewPropertyIdentifier(arg Arg) (*PropertyIdentifier, error) {
	s := &PropertyIdentifier{
		vendorRange: vendorRange{512, 4194303},
		enumerations: map[string]uint64{
			"absenteeLimit":                    244,
			"acceptedModes":                    175,
			"accessAlarmEvents":                245,
			"accessDoors":                      246,
			"accessEvent":                      247,
			"accessEventAuthenticationFactor":  248,
			"accessEventCredential":            249,
			"accessEventTag":                   322,
			"accessEventTime":                  250,
			"accessTransactionEvents":          251,
			"accompaniment":                    252,
			"accompanimentTime":                253,
			"ackedTransitions":                 0,
			"ackRequired":                      1,
			"action":                           2,
			"actionText":                       3,
			"activationTime":                   254,
			"activeAuthenticationPolicy":       255,
			"activeCovMultipleSubscriptions":   481,
			"activeCovSubscriptions":           152,
			"activeText":                       4,
			"activeVtSessions":                 5,
			"actualShedLevel":                  212,
			"adjustValue":                      176,
			"alarmValue":                       6,
			"alarmValues":                      7,
			"alignIntervals":                   193,
			"all":                              8,
			"allowGroupDelayInhibit":           365,
			"allWritesSuccessful":              9,
			"apduLength":                       399,
			"apduSegmentTimeout":               10,
			"apduTimeout":                      11,
			"applicationSoftwareVersion":       12,
			"archive":                          13,
			"assignedAccessRights":             256,
			"assignedLandingCalls":             447,
			"attemptedSamples":                 124,
			"auditableOperations":              501,
			"auditablePriorityFilter":          500,
			"auditLevel":                       498,
			"auditNotificationRecipient":       499,
			"auditSourceReporter":              497,
			"authenticationFactors":            257,
			"authenticationPolicyList":         258,
			"authenticationPolicyNames":        259,
			"authenticationStatus":             260,
			"authorizationExemptions":          364,
			"authorizationMode":                261,
			"autoSlaveDiscovery":               169,
			"averageValue":                     125,
			"backupAndRestoreState":            338,
			"backupFailureTimeout":             153,
			"backupPreparationTime":            339,
			"bacnetIPGlobalAddress":            407,
			"bacnetIPMode":                     408,
			"bacnetIPMulticastAddress":         409,
			"bacnetIPNATTraversal":             410,
			"bacnetIPUDPPort":                  412,
			"bacnetIPv6Mode":                   435,
			"bacnetIPv6MulticastAddress":       440,
			"bacnetIPv6UDPPort":                438,
			"baseDeviceSecurityPolicy":         327,
			"bbmdAcceptFDRegistrations":        413,
			"bbmdBroadcastDistributionTable":   414,
			"bbmdForeignDeviceTable":           415,
			"belongsTo":                        262,
			"bias":                             14,
			"bitMask":                          342,
			"bitText":                          343,
			"blinkWarnEnable":                  373,
			"bufferSize":                       126,
			"carAssignedDirection":             448,
			"carDoorCommand":                   449,
			"carDoorStatus":                    450,
			"carDoorText":                      451,
			"carDoorZone":                      452,
			"carDriveStatus":                   453,
			"carLoad":                          454,
			"carLoadUnits":                     455,
			"carMode":                          456,
			"carMovingDirection":               457,
			"carPosition":                      458,
			"changeOfStateCount":               15,
			"changeOfStateTime":                16,
			"changesPending":                   416,
			"channelNumber":                    366,
			"clientCovIncrement":               127,
			"command":                          417,
			"commandTimeArray":                 430,
			"configurationFiles":               154,
			"controlGroups":                    367,
			"controlledVariableReference":      19,
			"controlledVariableUnits":          20,
			"controlledVariableValue":          21,
			"count":                            177,
			"countBeforeChange":                178,
			"countChangeTime":                  179,
			"covIncrement":                     22,
			"covPeriod":                        180,
			"covResubscriptionInterval":        128,
			"covuPeriod":                       349,
			"covuRecipients":                   350,
			"credentialDisable":                263,
			"credentials":                      265,
			"credentialsInZone":                266,
			"credentialStatus":                 264,
			"currentCommandPriority":           431,
			"databaseRevision":                 155,
			"dateList":                         23,
			"daylightSavingsStatus":            24,
			"daysRemaining":                    267,
			"deadband":                         25,
			"defaultFadeTime":                  374,
			"defaultPresentValue":              492,
			"defaultRampRate":                  375,
			"defaultStepIncrement":             376,
			"defaultSubordinateRelationship":   490,
			"defaultTimeout":                   393,
			"deleteOnForward":                  502,
			"deployedProfileLocation":          484,
			"derivativeConstant":               26,
			"derivativeConstantUnits":          27,
			"description":                      28,
			"descriptionOfHalt":                29,
			"deviceAddressBinding":             30,
			"deviceType":                       31,
			"deviceUUID":                       507,
			"directReading":                    156,
			"distributionKeyRevision":          328,
			"doNotHide":                        329,
			"doorAlarmState":                   226,
			"doorExtendedPulseTime":            227,
			"doorMembers":                      228,
			"doorOpenTooLongTime":              229,
			"doorPulseTime":                    230,
			"doorStatus":                       231,
			"doorUnlockDelayTime":              232,
			"dutyWindow":                       213,
			"effectivePeriod":                  32,
			"egressActive":                     386,
			"egressTime":                       377,
			"elapsedActiveTime":                33,
			"elevatorGroup":                    459,
			"enable":                           133,
			"energyMeter":                      460,
			"energyMeterRef":                   461,
			"entryPoints":                      268,
			"errorLimit":                       34,
			"escalatorMode":                    462,
			"eventAlgorithmInhibit":            354,
			"eventAlgorithmInhibitRef":         355,
			"eventDetectionEnable":             353,
			"eventEnable":                      35,
			"eventMessageTexts":                351,
			"eventMessageTextsConfig":          352,
			"eventParameters":                  83,
			"eventState":                       36,
			"eventTimeStamps":                  130,
			"eventType":                        37,
			"exceptionSchedule":                38,
			"executionDelay":                   368,
			"exitPoints":                       269,
			"expectedShedLevel":                214,
			"expirationTime":                   270,
			"extendedTimeEnable":               271,
			"failedAttemptEvents":              272,
			"failedAttempts":                   273,
			"failedAttemptsTime":               274,
			"faultHighLimit":                   388,
			"faultLowLimit":                    389,
			"faultParameters":                  358,
			"faultSignals":                     463,
			"faultType":                        359,
			"faultValues":                      39,
			"fdBBMDAddress":                    418,
			"fdSubscriptionLifetime":           419,
			"feedbackValue":                    40,
			"fileAccessMethod":                 41,
			"fileSize":                         42,
			"fileType":                         43,
			"firmwareRevision":                 44,
			"floorNumber":                      506,
			"floorText":                        464,
			"fullDutyBaseline":                 215,
			"globalIdentifier":                 323,
			"groupID":                          465,
			"groupMemberNames":                 346,
			"groupMembers":                     345,
			"groupMode":                        467,
			"higherDeck":                       468,
			"highLimit":                        45,
			"inactiveText":                     46,
			"initialTimeout":                   394,
			"inProcess":                        47,
			"inProgress":                       378,
			"inputReference":                   181,
			"installationID":                   469,
			"instanceOf":                       48,
			"instantaneousPower":               379,
			"integralConstant":                 49,
			"integralConstantUnits":            50,
			"interfaceValue":                   387,
			"intervalOffset":                   195,
			"ipAddress":                        400,
			"ipDefaultGateway":                 401,
			"ipDHCPEnable":                     402,
			"ipDHCPLeaseTime":                  403,
			"ipDHCPLeaseTimeRemaining":         404,
			"ipDHCPServer":                     405,
			"ipDNSServer":                      406,
			"ipSubnetMask":                     411,
			"ipv6Address":                      436,
			"ipv6AutoAddressingEnable":         442,
			"ipv6DefaultGateway":               439,
			"ipv6DHCPLeaseTime":                443,
			"ipv6DHCPLeaseTimeRemaining":       444,
			"ipv6DHCPServer":                   445,
			"ipv6DNSServer":                    441,
			"ipv6PrefixLength":                 437,
			"ipv6ZoneIndex":                    446,
			"issueConfirmedNotifications":      51,
			"isUTC":                            344,
			"keySets":                          330,
			"landingCallControl":               471,
			"landingCalls":                     470,
			"landingDoorStatus":                472,
			"lastAccessEvent":                  275,
			"lastAccessPoint":                  276,
			"lastCommandTime":                  432,
			"lastCredentialAdded":              277,
			"lastCredentialAddedTime":          278,
			"lastCredentialRemoved":            279,
			"lastCredentialRemovedTime":        280,
			"lastKeyServer":                    331,
			"lastNotifyRecord":                 173,
			"lastPriority":                     369,
			"lastRestartReason":                196,
			"lastRestoreTime":                  157,
			"lastStateChange":                  395,
			"lastUseTime":                      281,
			"lifeSafetyAlarmValues":            166,
			"lightingCommand":                  380,
			"lightingCommandDefaultPriority":   381,
			"limitEnable":                      52,
			"limitMonitoringInterval":          182,
			"linkSpeed":                        420,
			"linkSpeedAutonegotiate":           422,
			"linkSpeeds":                       421,
			"listOfGroupMembers":               53,
			"listOfObjectPropertyReferences":   54,
			"listOfSessionKeys":                55,
			"localDate":                        56,
			"localForwardingOnly":              360,
			"localTime":                        57,
			"location":                         58,
			"lockout":                          282,
			"lockoutRelinquishTime":            283,
			"lockStatus":                       233,
			"logBuffer":                        131,
			"logDeviceObjectProperty":          132,
			"loggingObject":                    183,
			"loggingRecord":                    184,
			"loggingType":                      197,
			"logInterval":                      134,
			"lowDiffLimit":                     390,
			"lowerDeck":                        473,
			"lowLimit":                         59,
			"macAddress":                       423,
			"machineRoomID":                    474,
			"maintenanceRequired":              158,
			"makingCarCall":                    475,
			"manipulatedVariableReference":     60,
			"manualSlaveAddressBinding":        170,
			"maskedAlarmValues":                234,
			"masterExemption":                  284,
			"maxActualValue":                   382,
			"maxApduLengthAccepted":            62,
			"maxFailedAttempts":                285,
			"maximumOutput":                    61,
			"maximumSendDelay":                 503,
			"maximumValue":                     135,
			"maximumValueTimestamp":            149,
			"maxInfoFrames":                    63,
			"maxMaster":                        64,
			"maxPresValue":                     65,
			"maxSegmentsAccepted":              167,
			"memberOf":                         159,
			"members":                          286,
			"memberStatusFlags":                347,
			"minActualValue":                   383,
			"minimumOffTime":                   66,
			"minimumOnTime":                    67,
			"minimumOutput":                    68,
			"minimumValue":                     136,
			"minimumValueTimestamp":            150,
			"minPresValue":                     69,
			"mode":                             160,
			"modelName":                        70,
			"modificationDate":                 71,
			"monitoredObjects":                 504,
			"musterPoint":                      287,
			"negativeAccessRules":              288,
			"networkAccessSecurityPolicies":    332,
			"networkInterfaceName":             424,
			"networkNumber":                    425,
			"networkNumberQuality":             426,
			"networkType":                      427,
			"nextStoppingFloor":                476,
			"nodeSubtype":                      207,
			"nodeType":                         208,
			"notificationClass":                17,
			"notificationThreshold":            137,
			"notifyType":                       72,
			"numberOfApduRetries":              73,
			"numberOfAuthenticationPolicies":   289,
			"numberOfStates":                   74,
			"objectIdentifier":                 75,
			"objectList":                       76,
			"objectName":                       77,
			"objectPropertyReference":          78,
			"objectType":                       79,
			"occupancyCount":                   290,
			"occupancyCountAdjust":             291,
			"occupancyCountEnable":             292,
			"occupancyExemption":               293,
			"occupancyLowerLimit":              294,
			"occupancyLowerLimitEnforced":      295,
			"occupancyState":                   296,
			"occupancyUpperLimit":              297,
			"occupancyUpperLimitEnforced":      298,
			"operationDirection":               477,
			"operationExpected":                161,
			"optional":                         80,
			"outOfService":                     81,
			"outputUnits":                      82,
			"packetReorderTime":                333,
			"passbackExemption":                299,
			"passbackMode":                     300,
			"passbackTimeout":                  301,
			"passengerAlarm":                   478,
			"polarity":                         84,
			"portFilter":                       363,
			"positiveAccessRules":              302,
			"power":                            384,
			"powerMode":                        479,
			"prescale":                         185,
			"presentStage":                     493,
			"presentValue":                     85,
			"priority":                         86,
			"priorityArray":                    87,
			"priorityForWriting":               88,
			"processIdentifier":                89,
			"processIdentifierFilter":          361,
			"profileLocation":                  485,
			"profileName":                      168,
			"programChange":                    90,
			"programLocation":                  91,
			"programState":                     92,
			"propertyList":                     371,
			"proportionalConstant":             93,
			"proportionalConstantUnits":        94,
			"protocolLevel":                    482,
			"protocolObjectTypesSupported":     96,
			"protocolRevision":                 139,
			"protocolServicesSupported":        97,
			"protocolVersion":                  98,
			"pulseRate":                        186,
			"readOnly":                         99,
			"reasonForDisable":                 303,
			"reasonForHalt":                    100,
			"recipientList":                    102,
			"recordCount":                      141,
			"recordsSinceNotification":         140,
			"referencePort":                    483,
			"registeredCarCall":                480,
			"reliability":                      103,
			"reliabilityEvaluationInhibit":     357,
			"relinquishDefault":                104,
			"represents":                       491,
			"requestedShedLevel":               218,
			"requestedUpdateInterval":          348,
			"required":                         105,
			"resolution":                       106,
			"restartNotificationRecipients":    202,
			"restoreCompletionTime":            340,
			"restorePreparationTime":           341,
			"routingTable":                     428,
			"scale":                            187,
			"scaleFactor":                      188,
			"scheduleDefault":                  174,
			"securedStatus":                    235,
			"securityPDUTimeout":               334,
			"securityTimeWindow":               335,
			"segmentationSupported":            107,
			"sendNow":                          505,
			"serialNumber":                     372,
			"setpoint":                         108,
			"setpointReference":                109,
			"setting":                          162,
			"shedDuration":                     219,
			"shedLevelDescriptions":            220,
			"shedLevels":                       221,
			"silenced":                         163,
			"slaveAddressBinding":              171,
			"slaveProxyEnable":                 172,
			"stageNames":                       495,
			"stages":                           494,
			"startTime":                        142,
			"stateChangeValues":                396,
			"stateDescription":                 222,
			"stateText":                        110,
			"statusFlags":                      111,
			"stopTime":                         143,
			"stopWhenFull":                     144,
			"strikeCount":                      391,
			"structuredObjectList":             209,
			"subordinateAnnotations":           210,
			"subordinateList":                  211,
			"subordinateNodeTypes":             487,
			"subordinateRelationships":         489,
			"subordinateTags":                  488,
			"subscribedRecipients":             362,
			"supportedFormatClasses":           305,
			"supportedFormats":                 304,
			"supportedSecurityAlgorithms":      336,
			"systemStatus":                     112,
			"tags":                             486,
			"targetReferences":                 496,
			"threatAuthority":                  306,
			"threatLevel":                      307,
			"timeDelay":                        113,
			"timeDelayNormal":                  356,
			"timeOfActiveTimeReset":            114,
			"timeOfDeviceRestart":              203,
			"timeOfStateCountReset":            115,
			"timeOfStrikeCountReset":           392,
			"timerRunning":                     397,
			"timerState":                       398,
			"timeSynchronizationInterval":      204,
			"timeSynchronizationRecipients":    116,
			"totalRecordCount":                 145,
			"traceFlag":                        308,
			"trackingValue":                    164,
			"transactionNotificationClass":     309,
			"transition":                       385,
			"trigger":                          205,
			"units":                            117,
			"updateInterval":                   118,
			"updateKeySetTimeout":              337,
			"updateTime":                       189,
			"userExternalIdentifier":           310,
			"userInformationReference":         311,
			"userName":                         317,
			"userType":                         318,
			"usesRemaining":                    319,
			"utcOffset":                        119,
			"utcTimeSynchronizationRecipients": 206,
			"validSamples":                     146,
			"valueBeforeChange":                190,
			"valueChangeTime":                  192,
			"valueSet":                         191,
			"valueSource":                      433,
			"valueSourceArray":                 434,
			"varianceValue":                    151,
			"vendorIdentifier":                 120,
			"vendorName":                       121,
			"verificationTime":                 326,
			"virtualMACAddressTable":           429,
			"vtClassesSupported":               122,
			"weeklySchedule":                   123,
			"windowInterval":                   147,
			"windowSamples":                    148,
			"writeStatus":                      370,
			"zoneFrom":                         320,
			"zoneMembers":                      165,
			"zoneTo":                           321,
		},
	}
	var err error
	s.Enumerated, err = NewEnumerated(NoArgs)
	if err != nil {
		return nil, errors.Wrap(err, "error creating enumerated")
	}
	return s, nil
}
