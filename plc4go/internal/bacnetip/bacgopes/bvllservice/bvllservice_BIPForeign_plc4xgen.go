/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Code generated by "plc4xGenerator -type=BIPForeign -prefix=bvllservice_"; DO NOT EDIT.

package bvllservice

import (
	"context"
	"encoding/binary"
	"fmt"
	"github.com/apache/plc4x/plc4go/spi/utils"
)

var _ = fmt.Printf

func (d *BIPForeign) Serialize() ([]byte, error) {
	if d == nil {
		return nil, fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	wb := utils.NewWriteBufferByteBased(utils.WithByteOrderForByteBasedBuffer(binary.BigEndian))
	if err := d.SerializeWithWriteBuffer(context.Background(), wb); err != nil {
		return nil, err
	}
	return wb.GetBytes(), nil
}

func (d *BIPForeign) SerializeWithWriteBuffer(ctx context.Context, writeBuffer utils.WriteBuffer) error {
	if d == nil {
		return fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	if err := writeBuffer.PushContext("BIPForeign"); err != nil {
		return err
	}
	if err := d.BIPSAP.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}
	if err := d.ClientContract.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}
	if err := d.ServerContract.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}
	if err := d.OneShotTask.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}

	if err := writeBuffer.WriteInt64("registrationStatus", 64, int64(d.registrationStatus)); err != nil {
		return err
	}
	if d.bbmdAddress != nil {
		{
			_value := fmt.Sprintf("%v", d.bbmdAddress)

			if err := writeBuffer.WriteString("bbmdAddress", uint32(len(_value)*8), _value); err != nil {
				return err
			}
		}
	}
	if d.bbmdTimeToLive != nil {
		if err := writeBuffer.WriteUint16("bbmdTimeToLive", 16, *d.bbmdTimeToLive); err != nil {
			return err
		}
	}
	if d.registrationTimeoutTask != nil {
		{
			_value := fmt.Sprintf("%v", d.registrationTimeoutTask)

			if err := writeBuffer.WriteString("registrationTimeoutTask", uint32(len(_value)*8), _value); err != nil {
				return err
			}
		}
	}
	if err := writeBuffer.PopContext("BIPForeign"); err != nil {
		return err
	}
	return nil
}

func (d *BIPForeign) String() string {
	if alternateStringer, ok := any(d).(utils.AlternateStringer); ok {
		if alternateString, use := alternateStringer.AlternateString(); use {
			return alternateString
		}
	}
	wb := utils.NewWriteBufferBoxBased(utils.WithWriteBufferBoxBasedMergeSingleBoxes(), utils.WithWriteBufferBoxBasedOmitEmptyBoxes())
	if err := wb.WriteSerializable(context.Background(), d); err != nil {
		return err.Error()
	}
	return wb.GetBox().String()
}
