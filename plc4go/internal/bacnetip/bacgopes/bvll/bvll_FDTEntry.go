/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package bvll

import (
	"fmt"

	. "github.com/apache/plc4x/plc4go/internal/bacnetip/bacgopes/pdu"
)

type FDTEntry struct {
	FDAddress *Address
	FDTTL     uint16
	FDRemain  uint16
}

func (f *FDTEntry) Equals(other any) bool {
	if f == other {
		return true
	}
	otherEntry, ok := other.(*FDTEntry)
	if !ok {
		return false
	}
	return f.FDAddress.Equals(otherEntry.FDAddress) && f.FDTTL == otherEntry.FDTTL && f.FDRemain == otherEntry.FDRemain
}

func (f *FDTEntry) String() string {
	return fmt.Sprintf("%s (ttl: %d, remain: %d)", f.FDAddress, f.FDTTL, f.FDRemain)
}
