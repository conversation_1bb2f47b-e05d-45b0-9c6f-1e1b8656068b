/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Code generated by "plc4xGenerator -type=Application -prefix=app_"; DO NOT EDIT.

package app

import (
	"context"
	"encoding/binary"
	"fmt"
	"github.com/apache/plc4x/plc4go/spi/utils"
)

var _ = fmt.Printf

func (d *Application) Serialize() ([]byte, error) {
	if d == nil {
		return nil, fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	wb := utils.NewWriteBufferByteBased(utils.WithByteOrderForByteBasedBuffer(binary.BigEndian))
	if err := d.SerializeWithWriteBuffer(context.Background(), wb); err != nil {
		return nil, err
	}
	return wb.GetBytes(), nil
}

func (d *Application) SerializeWithWriteBuffer(ctx context.Context, writeBuffer utils.WriteBuffer) error {
	if d == nil {
		return fmt.Errorf("(*DeviceInfoCache)(nil)")
	}
	if err := writeBuffer.PushContext("Application"); err != nil {
		return err
	}
	if err := d.ApplicationServiceElementContract.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}
	if err := d.Collector.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
		return err
	}
	if err := writeBuffer.PushContext("objectName", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	for _name, elem := range d.objectName {
		name := _name
		_value := fmt.Sprintf("%v", elem)

		if err := writeBuffer.WriteString(name, uint32(len(_value)*8), _value); err != nil {
			return err
		}
	}
	if err := writeBuffer.PopContext("objectName", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	if err := writeBuffer.PushContext("objectIdentifier", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	for _name, elem := range d.objectIdentifier {
		name := _name
		_value := fmt.Sprintf("%v", elem)

		if err := writeBuffer.WriteString(name, uint32(len(_value)*8), _value); err != nil {
			return err
		}
	}
	if err := writeBuffer.PopContext("objectIdentifier", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	{
		_value := fmt.Sprintf("%v", d.localDevice)

		if err := writeBuffer.WriteString("localDevice", uint32(len(_value)*8), _value); err != nil {
			return err
		}
	}
	if d.localAddress != nil {
		{
			_value := fmt.Sprintf("%v", d.localAddress)

			if err := writeBuffer.WriteString("localAddress", uint32(len(_value)*8), _value); err != nil {
				return err
			}
		}
	}

	if d.deviceInfoCache != nil {
		if serializableField, ok := any(d.deviceInfoCache).(utils.Serializable); ok {
			if err := writeBuffer.PushContext("deviceInfoCache"); err != nil {
				return err
			}
			if err := serializableField.SerializeWithWriteBuffer(ctx, writeBuffer); err != nil {
				return err
			}
			if err := writeBuffer.PopContext("deviceInfoCache"); err != nil {
				return err
			}
		} else {
			stringValue := fmt.Sprintf("%v", d.deviceInfoCache)
			if err := writeBuffer.WriteString("deviceInfoCache", uint32(len(stringValue)*8), stringValue); err != nil {
				return err
			}
		}
	}
	if err := writeBuffer.PushContext("controllers", utils.WithRenderAsList(true)); err != nil {
		return err
	}
	for _name, elem := range d.controllers {
		name := _name
		_value := fmt.Sprintf("%v", elem)

		if err := writeBuffer.WriteString(name, uint32(len(_value)*8), _value); err != nil {
			return err
		}
	}
	if err := writeBuffer.PopContext("controllers", utils.WithRenderAsList(true)); err != nil {
		return err
	}

	if err := writeBuffer.WriteBit("_startupDisabled", d._startupDisabled); err != nil {
		return err
	}
	if err := writeBuffer.PopContext("Application"); err != nil {
		return err
	}
	return nil
}

func (d *Application) String() string {
	if alternateStringer, ok := any(d).(utils.AlternateStringer); ok {
		if alternateString, use := alternateStringer.AlternateString(); use {
			return alternateString
		}
	}
	wb := utils.NewWriteBufferBoxBased(utils.WithWriteBufferBoxBasedMergeSingleBoxes(), utils.WithWriteBufferBoxBasedOmitEmptyBoxes())
	if err := wb.WriteSerializable(context.Background(), d); err != nil {
		return err.Error()
	}
	return wb.GetBox().String()
}
