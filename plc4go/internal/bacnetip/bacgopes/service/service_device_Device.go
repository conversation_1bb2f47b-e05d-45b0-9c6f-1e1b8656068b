/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package service

import (
	"strconv"
	"strings"

	"github.com/apache/plc4x/plc4go/protocols/bacnetip/readwrite/model"
)

func ObjectIdentifierStringToTuple(objectIdentifier string) (objectType uint16, instance uint32) {
	split := strings.Split(objectIdentifier, ":")
	if len(split) != 2 {
		panic("broken object identifier")
	}
	bacnetObjectType, ok := model.BACnetObjectTypeByName(strings.ToUpper(split[0]))
	if ok {
		objectType = uint16(bacnetObjectType)
	} else {
		parsedObjectType, err := strconv.Atoi(split[0])
		if err != nil {
			panic(err)
		}
		objectType = uint16(parsedObjectType)
	}
	parsedInstance, err := strconv.Atoi(split[1])
	if err != nil {
		panic(err)
	}
	instance = uint32(parsedInstance)
	return
}
