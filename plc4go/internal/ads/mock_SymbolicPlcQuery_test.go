/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Code generated by mockery v2.42.2. DO NOT EDIT.

package ads

import mock "github.com/stretchr/testify/mock"

// MockSymbolicPlcQuery is an autogenerated mock type for the SymbolicPlcQuery type
type MockSymbolicPlcQuery struct {
	mock.Mock
}

type MockSymbolicPlcQuery_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSymbolicPlcQuery) EXPECT() *MockSymbolicPlcQuery_Expecter {
	return &MockSymbolicPlcQuery_Expecter{mock: &_m.Mock}
}

// GetQueryString provides a mock function with given fields:
func (_m *MockSymbolicPlcQuery) GetQueryString() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetQueryString")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSymbolicPlcQuery_GetQueryString_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetQueryString'
type MockSymbolicPlcQuery_GetQueryString_Call struct {
	*mock.Call
}

// GetQueryString is a helper method to define mock.On call
func (_e *MockSymbolicPlcQuery_Expecter) GetQueryString() *MockSymbolicPlcQuery_GetQueryString_Call {
	return &MockSymbolicPlcQuery_GetQueryString_Call{Call: _e.mock.On("GetQueryString")}
}

func (_c *MockSymbolicPlcQuery_GetQueryString_Call) Run(run func()) *MockSymbolicPlcQuery_GetQueryString_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSymbolicPlcQuery_GetQueryString_Call) Return(_a0 string) *MockSymbolicPlcQuery_GetQueryString_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSymbolicPlcQuery_GetQueryString_Call) RunAndReturn(run func() string) *MockSymbolicPlcQuery_GetQueryString_Call {
	_c.Call.Return(run)
	return _c
}

// GetSymbolicAddressPattern provides a mock function with given fields:
func (_m *MockSymbolicPlcQuery) GetSymbolicAddressPattern() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSymbolicAddressPattern")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSymbolicAddressPattern'
type MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call struct {
	*mock.Call
}

// GetSymbolicAddressPattern is a helper method to define mock.On call
func (_e *MockSymbolicPlcQuery_Expecter) GetSymbolicAddressPattern() *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call {
	return &MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call{Call: _e.mock.On("GetSymbolicAddressPattern")}
}

func (_c *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call) Run(run func()) *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call) Return(_a0 string) *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call) RunAndReturn(run func() string) *MockSymbolicPlcQuery_GetSymbolicAddressPattern_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSymbolicPlcQuery creates a new instance of MockSymbolicPlcQuery. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSymbolicPlcQuery(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSymbolicPlcQuery {
	mock := &MockSymbolicPlcQuery{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
