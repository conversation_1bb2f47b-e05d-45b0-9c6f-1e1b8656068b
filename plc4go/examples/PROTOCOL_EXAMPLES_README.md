# PLC4Go协议示例集合

这个目录包含了plc4go支持的主要协议的完整使用示例。

## 📁 示例文件

### 1. `s7_example.go` - Siemens S7协议
- **适用设备**: S7-300, S7-400, S7-1200, S7-1500
- **传输方式**: TCP/IP (端口102)
- **功能**: 读取、写入、订阅

### 2. `knxnetip_example.go` - KNXnet/IP协议  
- **适用设备**: KNX总线设备通过IP网关
- **传输方式**: UDP (端口3671)
- **功能**: 读取、写入、订阅群组地址

### 3. `modbus_example.go` - Modbus协议
- **适用设备**: 支持Modbus的工业设备
- **传输方式**: TCP (端口502), RTU/ASCII (串口)
- **功能**: 读取、写入线圈和寄存器

## 🚀 快速开始

### 运行示例

```bash
# 进入示例目录
cd examples

# 运行S7示例
go run s7_example.go

# 运行KNX示例  
go run knxnetip_example.go

# 运行Modbus示例
go run modbus_example.go
```

### 修改连接参数

在运行前，请修改各示例文件中的连接字符串：

```go
// S7示例
connectionString := "s7://*************:102?remote-rack=0&remote-slot=2"

// KNX示例
connectionString := "knxnet-ip://*************:3671"

// Modbus示例
connectionString := "modbus-tcp://*************:502?unit-identifier=1"
```

## 📋 协议对比

| 协议 | 连接方式 | 默认端口 | 主要用途 | 标签格式示例 |
|------|----------|----------|----------|--------------|
| **S7** | TCP | 102 | 工业自动化 | `%DB1:0:INT`, `%I0.0:BOOL` |
| **KNXnet/IP** | UDP | 3671 | 楼宇自动化 | `1/2/3:DPT_Switch` |
| **Modbus** | TCP/Serial | 502 | 工业通信 | `holding-register:1:INT` |

## 🏷️ 标签地址格式详解

### S7协议标签
```go
// 基本内存区域
"%I0.0:BOOL"        // 输入I0.0
"%Q1.5:BOOL"        // 输出Q1.5  
"%M10:BYTE"         // 标志M10

// 数据块
"%DB1:0:INT"        // DB1.DBW0
"%DB1:4:REAL"       // DB1.DBD4
"%DB1:10:STRING(20)" // DB1字符串
```

### KNXnet/IP标签
```go
// 群组地址 (3级)
"1/2/3:DPT_Switch"     // 主群组1/中群组2/子群组3
"1/2/3:DPT_Scaling"    // 调光值 (0-100%)
"1/2/3:DPT_Value_Temp" // 温度值

// 群组地址 (2级)  
"1/515:DPT_Switch"     // 主群组1/子群组515

// 设备地址
"1.2.3#0/1/1[1]"       // 设备1.2.3的属性
```

### Modbus标签
```go
// PLC4X格式
"coil:1:BOOL"                    // 线圈1
"discrete-input:1:BOOL"          // 离散输入1
"input-register:1:INT"           // 输入寄存器1
"holding-register:1:INT"         // 保持寄存器1

// 数字格式
"0x0001:BOOL"                    // 线圈地址1
"1x0001:BOOL"                    // 离散输入地址1  
"3x0001:INT"                     // 输入寄存器地址1
"4x0001:INT"                     // 保持寄存器地址1

// 数组格式
"holding-register:10:INT[5]"     // 保持寄存器10-14
```

## 🔧 连接字符串格式

### S7连接字符串
```
s7://host:port?参数=值

常用参数:
- local-rack=1          // 本地机架号
- local-slot=1          // 本地槽号  
- remote-rack=0         // 远程机架号
- remote-slot=2         // 远程槽号
- pdu-size=1024         // PDU大小
```

### KNXnet/IP连接字符串
```
knxnet-ip://host:port

默认端口: 3671
传输协议: UDP
```

### Modbus连接字符串
```
# Modbus TCP
modbus-tcp://host:port?unit-identifier=1

# Modbus RTU (串口)
modbus-rtu:///dev/ttyUSB0?baud-rate=9600&data-bits=8&parity=none&stop-bits=1&unit-identifier=1

# Modbus ASCII (串口)  
modbus-ascii:///dev/ttyUSB0?baud-rate=9600&data-bits=7&parity=even&stop-bits=1&unit-identifier=1
```

## 💡 最佳实践

### 1. 错误处理
```go
connectionResult := <-driverManager.GetConnection(connectionString)
if connectionResult.GetErr() != nil {
    log.Fatalf("连接失败: %v", connectionResult.GetErr())
}
```

### 2. 资源管理
```go
defer func() {
    if err := driverManager.Close(); err != nil {
        log.Printf("Error closing driver manager: %v", err)
    }
}()
defer connection.BlockingClose()
```

### 3. 批量操作
```go
// 批量读取多个标签
readRequestBuilder.
    AddTagAddress("tag1", "address1").
    AddTagAddress("tag2", "address2").
    AddTagAddress("tag3", "address3")
```

### 4. 订阅处理
```go
// 添加事件消费者
subscriptionBuilder.AddPreRegisteredConsumer("monitor", func(event model.PlcSubscriptionEvent) {
    for _, tagName := range event.GetTagNames() {
        value := event.GetValue(tagName)
        fmt.Printf("变化: %s = %v\n", tagName, value.GetValue())
    }
})
```

## 🔍 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连通性
   - 确认设备IP地址和端口
   - 检查防火墙设置

2. **标签地址错误**
   - 验证标签格式是否正确
   - 确认设备中存在对应地址
   - 检查数据类型匹配

3. **权限问题**
   - S7: 启用PUT/GET通信访问
   - KNX: 检查群组地址权限
   - Modbus: 确认设备支持相应功能码

### 调试技巧

```go
// 启用详细日志
import "github.com/apache/plc4x/plc4go/spi/options"

// 在创建驱动时添加日志选项
driver := s7.NewDriver(options.WithCustomLogger(logger))
```

## 📚 扩展阅读

- [PLC4X官方文档](https://plc4x.apache.org/)
- [S7协议规范](https://support.industry.siemens.com/)
- [KNX协议标准](https://www.knx.org/)
- [Modbus协议规范](https://modbus.org/)

---

**提示**: 这些示例提供了基础的使用模式，可以根据具体需求进行扩展和定制。
