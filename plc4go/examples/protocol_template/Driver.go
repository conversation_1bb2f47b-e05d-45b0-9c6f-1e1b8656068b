package myprotocol

import (
	"context"
	"net/url"

	"github.com/rs/zerolog"

	"github.com/apache/plc4x/plc4go/pkg/api"
	_default "github.com/apache/plc4x/plc4go/spi/default"
	"github.com/apache/plc4x/plc4go/spi/options"
	"github.com/apache/plc4x/plc4go/spi/transports"
)

// Driver 实现PlcDriver接口
type Driver struct {
	_default.DefaultDriver
	
	log      zerolog.Logger
	_options []options.WithOption
}

// NewDriver 创建新的协议驱动器
func NewDriver(_options ...options.WithOption) plc4go.PlcDriver {
	customLogger := options.ExtractCustomLoggerOrDefaultToGlobal(_options...)
	driver := &Driver{
		log:      customLogger,
		_options: _options,
	}
	
	// 注册驱动器信息
	driver.DefaultDriver = _default.NewDefaultDriver(
		driver,
		"myprotocol",              // 协议代码 - 用于连接字符串
		"My Custom Protocol",      // 协议名称
		"tcp",                     // 默认传输层
		NewTagHandler(_options...) // 标签处理器
	)
	
	return driver
}

// GetConnectionWithContext 创建协议连接
func (d *Driver) GetConnectionWithContext(ctx context.Context, transportUrl url.URL, 
	transports map[string]transports.Transport, driverOptions map[string][]string) <-chan plc4go.PlcConnectionConnectResult {
	
	d.log.Debug().
		Stringer("transportUrl", &transportUrl).
		Int("nTransports", len(transports)).
		Int("nDriverOptions", len(driverOptions)).
		Msg("Creating connection for my protocol")
	
	// 获取传输层实例
	transport, ok := transports[transportUrl.Scheme]
	if !ok {
		d.log.Error().
			Stringer("transportUrl", &transportUrl).
			Str("scheme", transportUrl.Scheme).
			Msg("Transport not found")
		ch := make(chan plc4go.PlcConnectionConnectResult, 1)
		ch <- _default.NewDefaultPlcConnectionConnectResult(nil, 
			errors.Errorf("couldn't find transport for scheme %s", transportUrl.Scheme))
		return ch
	}
	
	// 设置默认端口(如果需要)
	driverOptions["defaultTcpPort"] = []string{"1234"} // 你的协议默认端口
	
	// 创建传输实例
	transportInstance, err := transport.CreateTransportInstance(
		transportUrl,
		driverOptions,
		append(d._options, options.WithCustomLogger(d.log))...,
	)
	if err != nil {
		d.log.Error().Err(err).Msg("Error creating transport instance")
		ch := make(chan plc4go.PlcConnectionConnectResult, 1)
		ch <- _default.NewDefaultPlcConnectionConnectResult(nil, err)
		return ch
	}
	
	// 创建消息编解码器
	codec := NewMessageCodec(transportInstance, append(d._options, options.WithCustomLogger(d.log))...)
	d.log.Debug().Stringer("codec", codec).Msg("Created message codec")
	
	// 解析驱动配置
	configuration, err := ParseFromOptions(d.log, driverOptions)
	if err != nil {
		d.log.Error().Err(err).Msg("Invalid driver options")
		ch := make(chan plc4go.PlcConnectionConnectResult, 1)
		ch <- _default.NewDefaultPlcConnectionConnectResult(nil, err)
		return ch
	}
	
	// 创建连接实例
	connection := NewConnection(
		codec,
		configuration,
		d.GetPlcTagHandler(),
		driverOptions,
		append(d._options, options.WithCustomLogger(d.log))...,
	)
	
	d.log.Debug().Msg("Connection created, connecting now")
	return connection.ConnectWithContext(ctx)
}

// SupportsDiscovery 返回是否支持设备发现
func (d *Driver) SupportsDiscovery() bool {
	return false // 根据协议需求设置
}

// Close 关闭驱动器并清理资源
func (d *Driver) Close() error {
	d.log.Trace().Msg("Closing driver")
	return nil
}

// Configuration 协议配置结构
type Configuration struct {
	DeviceId     uint16
	Timeout      uint32
	MaxRetries   int
	CustomParam  string
}

// ParseFromOptions 从连接选项中解析配置
func ParseFromOptions(localLog zerolog.Logger, options map[string][]string) (Configuration, error) {
	configuration := Configuration{
		DeviceId:    1,
		Timeout:     5000,
		MaxRetries:  3,
		CustomParam: "default",
	}
	
	// 解析device-id参数
	if deviceIdString := getFromOptions(localLog, options, "device-id"); deviceIdString != "" {
		parsedInt, err := strconv.ParseUint(deviceIdString, 10, 16)
		if err != nil {
			return Configuration{}, errors.Wrap(err, "Error parsing device-id")
		}
		configuration.DeviceId = uint16(parsedInt)
	}
	
	// 解析timeout参数
	if timeoutString := getFromOptions(localLog, options, "timeout"); timeoutString != "" {
		parsedInt, err := strconv.ParseUint(timeoutString, 10, 32)
		if err != nil {
			return Configuration{}, errors.Wrap(err, "Error parsing timeout")
		}
		configuration.Timeout = uint32(parsedInt)
	}
	
	// 解析自定义参数
	if customParam := getFromOptions(localLog, options, "custom-param"); customParam != "" {
		configuration.CustomParam = customParam
	}
	
	return configuration, nil
}

// getFromOptions 从选项映射中获取值的辅助函数
func getFromOptions(localLog zerolog.Logger, options map[string][]string, key string) string {
	if optionValues, ok := options[key]; ok {
		if len(optionValues) <= 0 {
			localLog.Warn().
				Str("key", key).
				Msg("Option is present, but has no value")
			return ""
		}
		return optionValues[0]
	}
	return ""
}
