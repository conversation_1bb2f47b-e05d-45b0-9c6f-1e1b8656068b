package main

import (
	"fmt"
	"log"
	"time"

	plc4go "github.com/apache/plc4x/plc4go/pkg/api"
	"github.com/apache/plc4x/plc4go/pkg/api/drivers"
	"github.com/apache/plc4x/plc4go/pkg/api/model"
	"github.com/apache/plc4x/plc4go/spi/values"
)

func knxnetip() {
	// 创建驱动管理器
	driverManager := plc4go.NewPlcDriverManager()
	defer func() {
		if err := driverManager.Close(); err != nil {
			log.Printf("Error closing driver manager: %v", err)
		}
	}()

	// 注册KNX驱动 - 使用UDP传输
	drivers.RegisterKnxDriver(driverManager)

	// KNXnet/IP连接字符串格式: knxnet-ip://host:port
	// 默认端口: 3671
	connectionString := "knxnet-ip://*************:3671"
	fmt.Printf("正在连接到KNXnet/IP网关: %s\n", connectionString)

	// 获取连接
	connectionResult := <-driverManager.GetConnection(connectionString)
	if connectionResult.GetErr() != nil {
		log.Fatalf("连接失败: %v", connectionResult.GetErr())
	}

	connection := connectionResult.GetConnection()
	defer connection.BlockingClose()

	fmt.Println("连接成功!")
	time.Sleep(1 * time.Second)

	// 演示读取操作
	performKnxReadOperations(connection)

	// 演示写入操作
	performKnxWriteOperations(connection)

	// 演示订阅操作
	performKnxSubscriptionOperations(connection)

	fmt.Println("KNX操作完成!")
}

// 执行KNX读取操作
func performKnxReadOperations(connection plc4go.PlcConnection) {
	fmt.Println("\n=== KNX读取操作示例 ===")

	// 创建读取请求构建器
	readRequestBuilder := connection.ReadRequestBuilder()

	// KNX标签地址格式说明:
	// 群组地址格式:
	// - 3级: main/middle/sub:datatype (例如: 1/2/3:DPT_Switch)
	// - 2级: main/sub:datatype (例如: 1/515:DPT_Switch)
	// - 1级: address:datatype (例如: 2051:DPT_Switch)
	//
	// 设备地址格式:
	// - 属性: main.middle.sub#objectId/propertyId/propertyIndex[numElements]
	// - 内存: main.middle.sub#address:datatype[numElements]

	// 添加各种类型的KNX读取标签
	readRequestBuilder.
		AddTagAddress("light_switch", "1/1/1:DPT_Switch").    // 开关状态 (1位布尔值)
		AddTagAddress("dimmer_value", "1/1/2:DPT_Scaling").   // 调光值 (0-100%)
		AddTagAddress("temperature", "1/2/1:DPT_Value_Temp"). // 温度值 (2字节浮点)
		AddTagAddress("brightness", "1/2/2:DPT_Value_Lux").   // 亮度值 (2字节浮点)
		AddTagAddress("time_value", "1/3/1:DPT_TimeOfDay").   // 时间值
		AddTagAddress("date_value", "1/3/2:DPT_Date")         // 日期值

	// 构建读取请求
	readRequest, err := readRequestBuilder.Build()
	if err != nil {
		log.Printf("构建KNX读取请求失败: %v", err)
		return
	}

	// 执行读取请求
	fmt.Println("执行KNX读取请求...")
	readResult := <-readRequest.Execute()
	if readResult.GetErr() != nil {
		log.Printf("KNX读取请求执行失败: %v", readResult.GetErr())
		return
	}

	// 处理读取响应
	readResponse := readResult.GetResponse()

	// 显示读取结果
	for _, tagName := range readResponse.GetTagNames() {
		if readResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			value := readResponse.GetValue(tagName)
			fmt.Printf("KNX标签 %s: %v (类型: %T)\n", tagName, value.String(), value.GetPlcValueType())
		} else {
			fmt.Printf("KNX标签 %s 读取失败: %s\n", tagName, readResponse.GetResponseCode(tagName))
		}
	}
}

// 执行KNX写入操作
func performKnxWriteOperations(connection plc4go.PlcConnection) {
	fmt.Println("\n=== KNX写入操作示例 ===")

	// 创建写入请求构建器
	writeRequestBuilder := connection.WriteRequestBuilder()

	// 添加各种类型的KNX写入标签和值
	writeRequestBuilder.
		AddTagAddress("light_control", "1/1/10:DPT_Switch", values.NewPlcBOOL(true)).     // 开灯
		AddTagAddress("dimmer_control", "1/1/11:DPT_Scaling", values.NewPlcUSINT(75)).    // 调光到75%
		AddTagAddress("temp_setpoint", "1/2/10:DPT_Value_Temp", values.NewPlcREAL(22.5)). // 设置温度22.5°C
		AddTagAddress("scene_control", "1/4/1:DPT_SceneNumber", values.NewPlcUSINT(3))    // 激活场景3

	// 构建写入请求
	writeRequest, err := writeRequestBuilder.Build()
	if err != nil {
		log.Printf("构建KNX写入请求失败: %v", err)
		return
	}

	// 执行写入请求
	fmt.Println("执行KNX写入请求...")
	writeResult := <-writeRequest.Execute()
	if writeResult.GetErr() != nil {
		log.Printf("KNX写入请求执行失败: %v", writeResult.GetErr())
		return
	}

	// 处理写入响应
	writeResponse := writeResult.GetResponse()

	// 显示写入结果
	for _, tagName := range writeResponse.GetTagNames() {
		if writeResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			fmt.Printf("KNX标签 %s: 写入成功\n", tagName)
		} else {
			fmt.Printf("KNX标签 %s 写入失败: %s\n", tagName, writeResponse.GetResponseCode(tagName))
		}
	}
}

// 执行KNX订阅操作
func performKnxSubscriptionOperations(connection plc4go.PlcConnection) {
	fmt.Println("\n=== KNX订阅操作示例 ===")

	// 检查连接是否支持订阅
	if !connection.GetMetadata().CanSubscribe() {
		fmt.Println("当前KNX连接不支持订阅功能")
		return
	}

	// 创建订阅请求构建器
	subscriptionBuilder := connection.SubscriptionRequestBuilder()

	// 添加订阅标签 - 监控群组地址变化
	subscriptionBuilder.
		AddChangeOfStateTagAddress("light_monitor", "1/1/*:DPT_Switch").   // 监控所有1/1/x开关
		AddChangeOfStateTagAddress("temp_monitor", "1/2/*:DPT_Value_Temp") // 监控所有1/2/x温度

	// 添加事件消费者
	subscriptionBuilder.AddPreRegisteredConsumer("light_monitor", func(event model.PlcSubscriptionEvent) {
		for _, tagName := range event.GetTagNames() {
			value := event.GetValue(tagName)
			fmt.Printf("KNX灯光变化: %s = %v\n", tagName, value.String())
		}
	})

	subscriptionBuilder.AddPreRegisteredConsumer("temp_monitor", func(event model.PlcSubscriptionEvent) {
		for _, tagName := range event.GetTagNames() {
			value := event.GetValue(tagName)
			fmt.Printf("KNX温度变化: %s = %v°C\n", tagName, value.String())
		}
	})

	// 构建订阅请求
	subscriptionRequest, err := subscriptionBuilder.Build()
	if err != nil {
		log.Printf("构建KNX订阅请求失败: %v", err)
		return
	}

	// 执行订阅
	fmt.Println("开始KNX订阅...")
	subscriptionResult := <-subscriptionRequest.Execute()
	if subscriptionResult.GetErr() != nil {
		log.Printf("KNX订阅执行失败: %v", subscriptionResult.GetErr())
		return
	}

	fmt.Println("KNX订阅成功，监控10秒钟...")
	time.Sleep(10 * time.Second)

	// 取消订阅
	subscriptionResponse := subscriptionResult.GetResponse()
	for _, tagName := range subscriptionResponse.GetTagNames() {
		if handle, err := subscriptionResponse.GetSubscriptionHandle(tagName); handle != nil && err == nil {
			unsubscribeBuilder := connection.UnsubscriptionRequestBuilder()
			unsubscribeBuilder.AddHandles(handle)
			unsubscribeRequest, _ := unsubscribeBuilder.Build()
			<-unsubscribeRequest.Execute()
		}
	}
	fmt.Println("KNX订阅已取消")
}
