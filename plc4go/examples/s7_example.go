package main

import (
	"fmt"
	"log"
	"time"

	plc4go "github.com/apache/plc4x/plc4go/pkg/api"
	"github.com/apache/plc4x/plc4go/pkg/api/drivers"
	"github.com/apache/plc4x/plc4go/pkg/api/model"
	"github.com/apache/plc4x/plc4go/spi/values"
)

func s7() {
	// 创建驱动管理器 - 尝试Command+点击 NewPlcDriverManager
	driverManager := plc4go.NewPlcDriverManager()
	defer func() {
		if err := driverManager.Close(); err != nil {
			log.Printf("Error closing driver manager: %v", err)
		}
	}()

	// 注册S7驱动 - 尝试Command+点击 RegisterS7Driver
	drivers.RegisterS7Driver(driverManager)

	// S7连接字符串
	connectionString := "s7://192.168.1.100:102?local-rack=1&local-slot=1&remote-rack=0&remote-slot=2"
	fmt.Printf("正在连接到S7 PLC: %s\n", connectionString)

	// 获取连接 - 尝试Command+点击 GetConnection
	connectionResult := <-driverManager.GetConnection(connectionString)
	if connectionResult.GetErr() != nil {
		log.Fatalf("连接失败: %v", connectionResult.GetErr())
	}

	connection := connectionResult.GetConnection()
	defer connection.BlockingClose()

	fmt.Println("连接成功!")
	time.Sleep(1 * time.Second)

	// 演示读取操作
	performReadOperations(connection)

	// 演示写入操作
	performWriteOperations(connection)

	fmt.Println("操作完成!")
}

// 执行读取操作
func performReadOperations(connection plc4go.PlcConnection) {
	fmt.Println("\n=== 读取操作示例 ===")

	// 创建读取请求构建器 - 尝试Command+点击 ReadRequestBuilder
	readRequestBuilder := connection.ReadRequestBuilder()

	// 添加各种类型的读取标签 - 尝试Command+点击 AddTagAddress
	readRequestBuilder.
		AddTagAddress("input_bool", "%I0.0:BOOL").  // 读取输入I0.0的布尔值
		AddTagAddress("output_bool", "%Q0.1:BOOL"). // 读取输出Q0.1的布尔值
		AddTagAddress("marker_byte", "%M10:BYTE").  // 读取标志M10的字节值
		AddTagAddress("db_int", "%DB1:0:INT").      // 读取DB1.DBW0的整数值
		AddTagAddress("db_real", "%DB1:4:REAL")     // 读取DB1.DBD4的实数值

	// 构建读取请求 - 尝试Command+点击 Build
	readRequest, err := readRequestBuilder.Build()
	if err != nil {
		log.Printf("构建读取请求失败: %v", err)
		return
	}

	// 执行读取请求 - 尝试Command+点击 Execute
	fmt.Println("执行读取请求...")
	readResult := <-readRequest.Execute()
	if readResult.GetErr() != nil {
		log.Printf("读取请求执行失败: %v", readResult.GetErr())
		return
	}

	// 处理读取响应 - 尝试Command+点击 GetResponse
	readResponse := readResult.GetResponse()
	fmt.Printf("读取响应状态: %s\n", readResponse.GetResponseCode("input_bool"))

	// 显示读取结果 - 尝试Command+点击 GetTagNames, GetValue
	for _, tagName := range readResponse.GetTagNames() {
		if readResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			value := readResponse.GetValue(tagName)

			fmt.Printf("标签 %s: %s (类型: %s)\n", tagName, value.String(), value.GetPlcValueType().String())
		} else {
			fmt.Printf("标签 %s 读取失败: %s\n", tagName, readResponse.GetResponseCode(tagName))
		}
	}
}

// 执行写入操作
func performWriteOperations(connection plc4go.PlcConnection) {
	fmt.Println("\n=== 写入操作示例 ===")

	// 创建写入请求构建器 - 尝试Command+点击 WriteRequestBuilder
	writeRequestBuilder := connection.WriteRequestBuilder()

	// 添加各种类型的写入标签和值 - 尝试Command+点击 AddTagAddress, NewPlcBOOL等
	writeRequestBuilder.
		AddTagAddress("output_bool", "%Q1.0:BOOL", values.NewPlcBOOL(true)). // 写入布尔值true到Q1.0
		AddTagAddress("marker_byte", "%M20:BYTE", values.NewPlcBYTE(42)).    // 写入字节值42到M20
		AddTagAddress("db_int", "%DB2:0:INT", values.NewPlcINT(1234)).       // 写入整数1234到DB2.DBW0
		AddTagAddress("db_real", "%DB2:4:REAL", values.NewPlcREAL(3.14159))  // 写入实数到DB2.DBD4

	// 构建写入请求
	writeRequest, err := writeRequestBuilder.Build()
	if err != nil {
		log.Printf("构建写入请求失败: %v", err)
		return
	}

	// 执行写入请求
	fmt.Println("执行写入请求...")
	writeResult := <-writeRequest.Execute()
	if writeResult.GetErr() != nil {
		log.Printf("写入请求执行失败: %v", writeResult.GetErr())
		return
	}

	// 处理写入响应
	writeResponse := writeResult.GetResponse()

	// 显示写入结果
	for _, tagName := range writeResponse.GetTagNames() {
		if writeResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			fmt.Printf("标签 %s: 写入成功\n", tagName)
		} else {
			fmt.Printf("标签 %s 写入失败: %s\n", tagName, writeResponse.GetResponseCode(tagName))
		}
	}
}
