package main

import (
	"fmt"
	"net/url"
)

func main() {
	fmt.Println("=== URL解析演示 ===")
	
	// 测试不同协议的连接字符串
	connectionStrings := []string{
		"s7://*************:102?local-rack=1&local-slot=1&remote-rack=0&remote-slot=2&pdu-size=1024",
		"knxnet-ip://*************:3671",
		"modbus-tcp://*************:502?unit-identifier=1",
		"modbus-rtu:///dev/ttyUSB0?baud-rate=9600&data-bits=8&parity=none&stop-bits=1&unit-identifier=1",
		"opcua:tcp://*************:4840/OPCUA/SimulationServer",
	}
	
	for i, connectionString := range connectionStrings {
		fmt.Printf("\n--- 示例 %d ---\n", i+1)
		fmt.Printf("连接字符串: %s\n", connectionString)
		
		// 解析URL
		parsedUrl, err := url.Parse(connectionString)
		if err != nil {
			fmt.Printf("解析错误: %v\n", err)
			continue
		}
		
		// 显示解析结果
		fmt.Printf("解析结果:\n")
		fmt.Printf("  Scheme (协议):     %s\n", parsedUrl.Scheme)
		fmt.Printf("  Host (主机:端口):   %s\n", parsedUrl.Host)
		fmt.Printf("  Path (路径):       %s\n", parsedUrl.Path)
		fmt.Printf("  RawQuery (原始查询): %s\n", parsedUrl.RawQuery)
		fmt.Printf("  Fragment (片段):   %s\n", parsedUrl.Fragment)
		
		// 解析查询参数
		if parsedUrl.RawQuery != "" {
			queryParams := parsedUrl.Query()
			fmt.Printf("  查询参数:\n")
			for key, values := range queryParams {
				for _, value := range values {
					fmt.Printf("    %s = %s\n", key, value)
				}
			}
		}
		
		// 演示plc4go中的使用方式
		fmt.Printf("  plc4go使用:\n")
		fmt.Printf("    驱动名称: %s\n", parsedUrl.Scheme)
		fmt.Printf("    传输地址: %s\n", parsedUrl.Host)
		
		// 特殊处理：如果有Opaque数据
		if parsedUrl.Opaque != "" {
			fmt.Printf("  Opaque (不透明数据): %s\n", parsedUrl.Opaque)
			// 对于某些协议，可能需要重新解析Opaque部分
			if innerUrl, err := url.Parse(parsedUrl.Opaque); err == nil {
				fmt.Printf("    内部解析:\n")
				fmt.Printf("      Scheme: %s\n", innerUrl.Scheme)
				fmt.Printf("      Host: %s\n", innerUrl.Host)
				fmt.Printf("      Path: %s\n", innerUrl.Path)
			}
		}
	}
	
	// 演示plc4go中的实际使用流程
	fmt.Println("\n=== plc4go中的使用流程 ===")
	demonstratePlc4goUsage()
}

func demonstratePlc4goUsage() {
	connectionString := "s7://*************:102?local-rack=1&remote-slot=2"
	fmt.Printf("连接字符串: %s\n", connectionString)
	
	// 步骤1: 解析连接字符串
	connectionUrl, err := url.Parse(connectionString)
	if err != nil {
		fmt.Printf("解析失败: %v\n", err)
		return
	}
	
	// 步骤2: 提取协议名称 (用于查找驱动)
	driverName := connectionUrl.Scheme
	fmt.Printf("1. 驱动名称: %s\n", driverName)
	
	// 步骤3: 提取配置选项
	configOptions := connectionUrl.Query()
	fmt.Printf("2. 配置选项:\n")
	for key, values := range configOptions {
		fmt.Printf("   %s: %v\n", key, values)
	}
	
	// 步骤4: 提取传输信息
	transportHost := connectionUrl.Host
	fmt.Printf("3. 传输地址: %s\n", transportHost)
	
	// 步骤5: 构建传输URL (这是plc4go内部的处理)
	// 假设S7驱动的默认传输是TCP
	defaultTransport := "tcp"  // 这通常来自driver.GetDefaultTransport()
	transportUrl := url.URL{
		Scheme: defaultTransport,
		Host:   transportHost,
	}
	fmt.Printf("4. 传输URL: %s\n", transportUrl.String())
	
	// 步骤6: 显示最终的路由信息
	fmt.Printf("5. 路由信息:\n")
	fmt.Printf("   协议驱动: %s -> S7Driver\n", driverName)
	fmt.Printf("   传输层: %s -> TCPTransport\n", defaultTransport)
	fmt.Printf("   目标地址: %s\n", transportHost)
	fmt.Printf("   驱动配置: %v\n", configOptions)
}
