<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:testsuite xmlns:test="https://plc4x.apache.org/schemas/parser-serializer-testsuite.xsd"
                byteOrder="BIG_ENDIAN">

  <name>KNXNet/IP</name>

  <protocolName>knxnetip</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <!--testcase>
    <name>Causes Failure 1</name>
    <raw>0610042000180404ce002b0703010404025002bab8b838bb</raw>
    Raw CEMI Frame: bab8b838bb
    Raw CEMI Frame: ba

    Decoded as Extended Frame Format:
    group address: true
    hop count: 3
    extended frame format: 8 (1 0 0 0)
    source address: 11/8/56

    <raw>061004200018047ddf002b07030104040207029f9c9c9cdc</raw>
    Raw CEMI Frame: 9f9c9c9cdc
    Control Field: 9f

    Differences from normal:
    Repeat: True
    Last two reserved bits are true
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <TunnelingRequest className="org.apache.plc4x.java.knxnetip.readwrite.TunnelingRequest">
        <tunnelingRequestDataBlock className="org.apache.plc4x.java.knxnetip.readwrite.TunnelingRequestDataBlock">
          <communicationChannelId>125</communicationChannelId>
          <sequenceCounter>223</sequenceCounter>
        </tunnelingRequestDataBlock>
        <cemi className="org.apache.plc4x.java.knxnetip.readwrite.CEMIBusmonInd">
          <additionalInformationLength>7</additionalInformationLength>
          <additionalInformation isList="true">
            <additionalInformation className="org.apache.plc4x.java.knxnetip.readwrite.CEMIAdditionalInformationBusmonitorInfo">
              <frameErrorFlag>false</frameErrorFlag>
              <bitErrorFlag>false</bitErrorFlag>
              <parityErrorFlag>false</parityErrorFlag>
              <unknownFlag>false</unknownFlag>
              <lostFlag>false</lostFlag>
              <sequenceNumber>4</sequenceNumber>
            </additionalInformation>
            <additionalInformation className="org.apache.plc4x.java.knxnetip.readwrite.CEMIAdditionalInformationRelativeTimestamp">
              <relativeTimestamp className="org.apache.plc4x.java.knxnetip.readwrite.RelativeTimestamp">
                <timestamp>1794</timestamp>
              </relativeTimestamp>
            </additionalInformation>
          </additionalInformation>
          <cemiFrame className="org.apache.plc4x.java.knxnetip.readwrite.CEMIFramePollingData">
            <doNotRepeat>false</doNotRepeat>
            <priority>LOW</priority>
            <errorFlag>true</errorFlag>
          </cemiFrame>
        </cemi>
      </TunnelingRequest>
    </xml>
  </testcase>

  <testcase>
    <name>Causes Failure 2</name>
    <raw>0610042000180401c2002b0703010304025601bab8b838bb</raw>
    Raw CEMI Frame: bab8b838bb
    Control Field: ba
    First of the last two reserved bits is true
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <TunnelingRequest className="org.apache.plc4x.java.knxnetip.readwrite.TunnelingRequest">
        <tunnelingRequestDataBlock className="org.apache.plc4x.java.knxnetip.readwrite.TunnelingRequestDataBlock">
          <communicationChannelId>1</communicationChannelId>
          <sequenceCounter>194</sequenceCounter>
        </tunnelingRequestDataBlock>
        <cemi className="org.apache.plc4x.java.knxnetip.readwrite.CEMIBusmonInd">
          <additionalInformationLength>7</additionalInformationLength>
          <additionalInformation isList="true">
            <additionalInformation className="org.apache.plc4x.java.knxnetip.readwrite.CEMIAdditionalInformationBusmonitorInfo">
              <frameErrorFlag>false</frameErrorFlag>
              <bitErrorFlag>false</bitErrorFlag>
              <parityErrorFlag>false</parityErrorFlag>
              <unknownFlag>false</unknownFlag>
              <lostFlag>false</lostFlag>
              <sequenceNumber>3</sequenceNumber>
            </additionalInformation>
            <additionalInformation className="org.apache.plc4x.java.knxnetip.readwrite.CEMIAdditionalInformationRelativeTimestamp">
              <relativeTimestamp className="org.apache.plc4x.java.knxnetip.readwrite.RelativeTimestamp">
                <timestamp>22017</timestamp>
              </relativeTimestamp>
            </additionalInformation>
          </additionalInformation>
          <cemiFrame className="org.apache.plc4x.java.knxnetip.readwrite.CEMIFramePollingData">
            <doNotRepeat>true</doNotRepeat>
            <priority>URGENT</priority>
            <errorFlag>false</errorFlag>
          </cemiFrame>
        </cemi>
      </TunnelingRequest>
    </xml>
  </testcase-->

  <testcase>
    <name>Search Request</name>
    <raw>06100201000e0801c0a82ac8d6b4</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">513</msgType>
        <totalLength dataType="uint" bitLength="16">14</totalLength>
        <SearchRequest>
          <hpaiIDiscoveryEndpoint>
            <HPAIDiscoveryEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82ac8</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">54964</ipPort>
            </HPAIDiscoveryEndpoint>
          </hpaiIDiscoveryEndpoint>
        </SearchRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Search Response</name>
    <raw>
      06100202004c0801c0a82a0b0e5736010200ffff000000082d409852e000170c000ab327553647697261204b4e582f49502d5363686e6974747374656c6c6500000000000802020103010401
    </raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">514</msgType>
        <totalLength dataType="uint" bitLength="16">76</totalLength>
        <SearchResponse>
          <hpaiControlEndpoint>
            <HPAIControlEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82a0b</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">3671</ipPort>
            </HPAIControlEndpoint>
          </hpaiControlEndpoint>
          <dibDeviceInfo>
            <DIBDeviceInfo>
              <structureLength dataType="uint" bitLength="8">54</structureLength>
              <descriptionType dataType="uint" bitLength="8">1</descriptionType>
              <knxMedium>
                <KnxMedium dataType="uint" bitLength="8" stringRepresentation="MEDIUM_TP1">2</KnxMedium>
              </knxMedium>
              <deviceStatus>
                <DeviceStatus>
                  <reserved dataType="uint" bitLength="7">0</reserved>
                  <programMode dataType="bit" bitLength="1">false</programMode>
                </DeviceStatus>
              </deviceStatus>
              <knxAddress>
                <KnxAddress>
                  <mainGroup dataType="uint" bitLength="4">15</mainGroup>
                  <middleGroup dataType="uint" bitLength="4">15</middleGroup>
                  <subGroup dataType="uint" bitLength="8">255</subGroup>
                </KnxAddress>
              </knxAddress>
              <projectInstallationIdentifier>
                <ProjectInstallationIdentifier>
                  <projectNumber dataType="uint" bitLength="8">0</projectNumber>
                  <installationNumber dataType="uint" bitLength="8">0</installationNumber>
                </ProjectInstallationIdentifier>
              </projectInstallationIdentifier>
              <knxNetIpDeviceSerialNumber dataType="byte" bitLength="48">0x00082d409852</knxNetIpDeviceSerialNumber>
              <knxNetIpDeviceMulticastAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xe000170c</addr>
                </IPAddress>
              </knxNetIpDeviceMulticastAddress>
              <knxNetIpDeviceMacAddress>
                <MACAddress>
                  <addr dataType="byte" bitLength="48">0x000ab3275536</addr>
                </MACAddress>
              </knxNetIpDeviceMacAddress>
              <deviceFriendlyName dataType="byte" bitLength="240">0x47697261204b4e582f49502d5363686e6974747374656c6c650000000000</deviceFriendlyName>
            </DIBDeviceInfo>
          </dibDeviceInfo>
          <dibSuppSvcFamilies>
            <DIBSuppSvcFamilies>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <descriptionType dataType="uint" bitLength="8">2</descriptionType>
              <serviceIds isList="true">
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">2</serviceType>
                  <KnxNetIpCore>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpCore>
                </ServiceId>
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">3</serviceType>
                  <KnxNetIpDeviceManagement>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpDeviceManagement>
                </ServiceId>
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">4</serviceType>
                  <KnxNetIpTunneling>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpTunneling>
                </ServiceId>
              </serviceIds>
            </DIBSuppSvcFamilies>
          </dibSuppSvcFamilies>
        </SearchResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Description Request</name>
    <raw>06100203000e0801000000000000</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">515</msgType>
        <totalLength dataType="uint" bitLength="16">14</totalLength>
        <DescriptionRequest>
          <hpaiControlEndpoint>
            <HPAIControlEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0x00000000</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">0</ipPort>
            </HPAIControlEndpoint>
          </hpaiControlEndpoint>
        </DescriptionRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Description Response</name>
    <raw>
      06100204004436010200ffff000000082d409852e000170c000ab327553647697261204b4e582f49502d5363686e6974747374656c6c6500000000000802020103010401
    </raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">516</msgType>
        <totalLength dataType="uint" bitLength="16">68</totalLength>
        <DescriptionResponse>
          <dibDeviceInfo>
            <DIBDeviceInfo>
              <structureLength dataType="uint" bitLength="8">54</structureLength>
              <descriptionType dataType="uint" bitLength="8">1</descriptionType>
              <knxMedium>
                <KnxMedium dataType="uint" bitLength="8" stringRepresentation="MEDIUM_TP1">2</KnxMedium>
              </knxMedium>
              <deviceStatus>
                <DeviceStatus>
                  <reserved dataType="uint" bitLength="7">0</reserved>
                  <programMode dataType="bit" bitLength="1">false</programMode>
                </DeviceStatus>
              </deviceStatus>
              <knxAddress>
                <KnxAddress>
                  <mainGroup dataType="uint" bitLength="4">15</mainGroup>
                  <middleGroup dataType="uint" bitLength="4">15</middleGroup>
                  <subGroup dataType="uint" bitLength="8">255</subGroup>
                </KnxAddress>
              </knxAddress>
              <projectInstallationIdentifier>
                <ProjectInstallationIdentifier>
                  <projectNumber dataType="uint" bitLength="8">0</projectNumber>
                  <installationNumber dataType="uint" bitLength="8">0</installationNumber>
                </ProjectInstallationIdentifier>
              </projectInstallationIdentifier>
              <knxNetIpDeviceSerialNumber dataType="byte" bitLength="48">0x00082d409852</knxNetIpDeviceSerialNumber>
              <knxNetIpDeviceMulticastAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xe000170c</addr>
                </IPAddress>
              </knxNetIpDeviceMulticastAddress>
              <knxNetIpDeviceMacAddress>
                <MACAddress>
                  <addr dataType="byte" bitLength="48">0x000ab3275536</addr>
                </MACAddress>
              </knxNetIpDeviceMacAddress>
              <deviceFriendlyName dataType="byte" bitLength="240">0x47697261204b4e582f49502d5363686e6974747374656c6c650000000000</deviceFriendlyName>
            </DIBDeviceInfo>
          </dibDeviceInfo>
          <dibSuppSvcFamilies>
            <DIBSuppSvcFamilies>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <descriptionType dataType="uint" bitLength="8">2</descriptionType>
              <serviceIds isList="true">
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">2</serviceType>
                  <KnxNetIpCore>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpCore>
                </ServiceId>
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">3</serviceType>
                  <KnxNetIpDeviceManagement>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpDeviceManagement>
                </ServiceId>
                <ServiceId>
                  <serviceType dataType="uint" bitLength="8">4</serviceType>
                  <KnxNetIpTunneling>
                    <version dataType="uint" bitLength="8">1</version>
                  </KnxNetIpTunneling>
                </ServiceId>
              </serviceIds>
            </DIBSuppSvcFamilies>
          </dibSuppSvcFamilies>
        </DescriptionResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Connect Request</name>
    <!--raw>06100205001a0801c0a82a46f4310801c0a82a46f43204040200</raw-->
    <raw>06100205001a0801c0a82ac8d6b40801c0a82ac8d6b404040200</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">517</msgType>
        <totalLength dataType="uint" bitLength="16">26</totalLength>
        <ConnectionRequest>
          <hpaiDiscoveryEndpoint>
            <HPAIDiscoveryEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82ac8</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">54964</ipPort>
            </HPAIDiscoveryEndpoint>
          </hpaiDiscoveryEndpoint>
          <hpaiDataEndpoint>
            <HPAIDataEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82ac8</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">54964</ipPort>
            </HPAIDataEndpoint>
          </hpaiDataEndpoint>
          <connectionRequestInformation>
            <ConnectionRequestInformation>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <connectionType dataType="uint" bitLength="8">4</connectionType>
              <ConnectionRequestInformationTunnelConnection>
                <knxLayer>
                  <KnxLayer dataType="uint" bitLength="8" stringRepresentation="TUNNEL_LINK_LAYER">2</KnxLayer>
                </knxLayer>
                <reserved dataType="uint" bitLength="8">0</reserved>
              </ConnectionRequestInformationTunnelConnection>
            </ConnectionRequestInformation>
          </connectionRequestInformation>
        </ConnectionRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Connect Response</name>
    <raw>06100206001402000801c0a82a0b0e570404fffe</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">518</msgType>
        <totalLength dataType="uint" bitLength="16">20</totalLength>
        <ConnectionResponse>
          <communicationChannelId dataType="uint" bitLength="8">2</communicationChannelId>
          <status>
            <Status dataType="uint" bitLength="8" stringRepresentation="NO_ERROR">0</Status>
          </status>
          <hpaiDataEndpoint>
            <HPAIDataEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82a0b</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">3671</ipPort>
            </HPAIDataEndpoint>
          </hpaiDataEndpoint>
          <connectionResponseDataBlock>
            <ConnectionResponseDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <connectionType dataType="uint" bitLength="8">4</connectionType>
              <ConnectionResponseDataBlockTunnelConnection>
                <knxAddress>
                  <KnxAddress>
                    <mainGroup dataType="uint" bitLength="4">15</mainGroup>
                    <middleGroup dataType="uint" bitLength="4">15</middleGroup>
                    <subGroup dataType="uint" bitLength="8">254</subGroup>
                  </KnxAddress>
                </knxAddress>
              </ConnectionResponseDataBlockTunnelConnection>
            </ConnectionResponseDataBlock>
          </connectionResponseDataBlock>
        </ConnectionResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Connection State Request</name>
    <raw>06100207001002000801c0a82ac8d6b4</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">519</msgType>
        <totalLength dataType="uint" bitLength="16">16</totalLength>
        <ConnectionStateRequest>
          <communicationChannelId dataType="uint" bitLength="8">2</communicationChannelId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <hpaiControlEndpoint>
            <HPAIControlEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82ac8</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">54964</ipPort>
            </HPAIControlEndpoint>
          </hpaiControlEndpoint>
        </ConnectionStateRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Connection State Response</name>
    <raw>0610020800080200</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">520</msgType>
        <totalLength dataType="uint" bitLength="16">8</totalLength>
        <ConnectionStateResponse>
          <communicationChannelId dataType="uint" bitLength="8">2</communicationChannelId>
          <status>
            <Status dataType="uint" bitLength="8" stringRepresentation="NO_ERROR">0</Status>
          </status>
        </ConnectionStateResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Device Configuration Request</name>
    <raw>06100310001104670000fc000001531001</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">784</msgType>
        <totalLength dataType="uint" bitLength="16">17</totalLength>
        <DeviceConfigurationRequest>
          <deviceConfigurationRequestDataBlock>
            <DeviceConfigurationRequestDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <communicationChannelId dataType="uint" bitLength="8">103</communicationChannelId>
              <sequenceCounter dataType="uint" bitLength="8">0</sequenceCounter>
              <reserved dataType="uint" bitLength="8">0</reserved>
            </DeviceConfigurationRequestDataBlock>
          </deviceConfigurationRequestDataBlock>
          <cemi>
            <CEMI>
              <messageCode dataType="uint" bitLength="8">252</messageCode>
              <MPropReadReq>
                <interfaceObjectType dataType="uint" bitLength="16">0</interfaceObjectType>
                <objectInstance dataType="uint" bitLength="8">1</objectInstance>
                <propertyId dataType="uint" bitLength="8">83</propertyId>
                <numberOfElements dataType="uint" bitLength="4">1</numberOfElements>
                <startIndex dataType="uint" bitLength="12">1</startIndex>
              </MPropReadReq>
            </CEMI>
          </cemi>
        </DeviceConfigurationRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Device Configuration Ack</name>
    <raw>06100311000a04670000</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">785</msgType>
        <totalLength dataType="uint" bitLength="16">10</totalLength>
        <DeviceConfigurationAck>
          <deviceConfigurationAckDataBlock>
            <DeviceConfigurationAckDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <communicationChannelId dataType="uint" bitLength="8">103</communicationChannelId>
              <sequenceCounter dataType="uint" bitLength="8">0</sequenceCounter>
              <status>
                <Status dataType="uint" bitLength="8" stringRepresentation="NO_ERROR">0</Status>
              </status>
            </DeviceConfigurationAckDataBlock>
          </deviceConfigurationAckDataBlock>
        </DeviceConfigurationAck>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Disconnect Request</name>
    <raw>06100209001001000801c0a82a0b0e57</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">521</msgType>
        <totalLength dataType="uint" bitLength="16">16</totalLength>
        <DisconnectRequest>
          <communicationChannelId dataType="uint" bitLength="8">1</communicationChannelId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <hpaiControlEndpoint>
            <HPAIControlEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82a0b</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">3671</ipPort>
            </HPAIControlEndpoint>
          </hpaiControlEndpoint>
        </DisconnectRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Disconnect Response</name>
    <raw>0610020a00086600</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">522</msgType>
        <totalLength dataType="uint" bitLength="16">8</totalLength>
        <DisconnectResponse>
          <communicationChannelId dataType="uint" bitLength="8">102</communicationChannelId>
          <status>
            <Status dataType="uint" bitLength="8" stringRepresentation="NO_ERROR">0</Status>
          </status>
        </DisconnectResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Tunneling Request</name>
    <raw>061004200015040200002900bce0220a120c010081</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">1056</msgType>
        <totalLength dataType="uint" bitLength="16">21</totalLength>
        <TunnelingRequest>
          <tunnelingRequestDataBlock>
            <TunnelingRequestDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <communicationChannelId dataType="uint" bitLength="8">2</communicationChannelId>
              <sequenceCounter dataType="uint" bitLength="8">0</sequenceCounter>
              <reserved dataType="uint" bitLength="8">0</reserved>
            </TunnelingRequestDataBlock>
          </tunnelingRequestDataBlock>
          <cemi>
            <CEMI>
              <messageCode dataType="uint" bitLength="8">41</messageCode>
              <LDataInd>
                <additionalInformationLength dataType="uint" bitLength="8">0</additionalInformationLength>
                <additionalInformation isList="true">
                </additionalInformation>
                <dataFrame>
                  <LDataFrame>
                    <frameType dataType="bit" bitLength="1">true</frameType>
                    <polling dataType="bit" bitLength="1">false</polling>
                    <notRepeated dataType="bit" bitLength="1">true</notRepeated>
                    <notAckFrame dataType="bit" bitLength="1">true</notAckFrame>
                    <priority>
                      <CEMIPriority dataType="uint" bitLength="2" stringRepresentation="LOW">3</CEMIPriority>
                    </priority>
                    <acknowledgeRequested dataType="bit" bitLength="1">false</acknowledgeRequested>
                    <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                    <LDataExtended>
                      <groupAddress dataType="bit" bitLength="1">true</groupAddress>
                      <hopCount dataType="uint" bitLength="3">6</hopCount>
                      <extendedFrameFormat dataType="uint" bitLength="4">0</extendedFrameFormat>
                      <sourceAddress>
                        <KnxAddress>
                          <mainGroup dataType="uint" bitLength="4">2</mainGroup>
                          <middleGroup dataType="uint" bitLength="4">2</middleGroup>
                          <subGroup dataType="uint" bitLength="8">10</subGroup>
                        </KnxAddress>
                      </sourceAddress>
                      <destinationAddress dataType="byte" bitLength="16">0x120c</destinationAddress>
                      <dataLength dataType="uint" bitLength="8">1</dataLength>
                      <apdu>
                        <Apdu>
                          <control dataType="uint" bitLength="1">0</control>
                          <numbered dataType="bit" bitLength="1">false</numbered>
                          <counter dataType="uint" bitLength="4">0</counter>
                          <ApduDataContainer>
                            <dataApdu>
                              <ApduData>
                                <apciType dataType="uint" bitLength="4">2</apciType>
                                <ApduDataGroupValueWrite>
                                  <dataFirstByte dataType="int" bitLength="6">1</dataFirstByte>
                                  <data dataType="byte" bitLength="0">0x</data>
                                </ApduDataGroupValueWrite>
                              </ApduData>
                            </dataApdu>
                          </ApduDataContainer>
                        </Apdu>
                      </apdu>
                    </LDataExtended>
                  </LDataFrame>
                </dataFrame>
              </LDataInd>
            </CEMI>
          </cemi>
        </TunnelingRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Tunneling Request (Busmon)</name>
    <raw>06100420001c046b00002b0703010504024502bc360a1e0ce100810d</raw>
    <!--
    Raw CEMI Frame: bc360a1e0ce100810d
    Control Field: bc
    -->
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">1056</msgType>
        <totalLength dataType="uint" bitLength="16">28</totalLength>
        <TunnelingRequest>
          <tunnelingRequestDataBlock>
            <TunnelingRequestDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <communicationChannelId dataType="uint" bitLength="8">107</communicationChannelId>
              <sequenceCounter dataType="uint" bitLength="8">0</sequenceCounter>
              <reserved dataType="uint" bitLength="8">0</reserved>
            </TunnelingRequestDataBlock>
          </tunnelingRequestDataBlock>
          <cemi>
            <CEMI>
              <messageCode dataType="uint" bitLength="8">43</messageCode>
              <LBusmonInd>
                <additionalInformationLength dataType="uint" bitLength="8">7</additionalInformationLength>
                <additionalInformation isList="true">
                  <CEMIAdditionalInformation>
                    <additionalInformationType dataType="uint" bitLength="8">3</additionalInformationType>
                    <CEMIAdditionalInformationBusmonitorInfo>
                      <len dataType="uint" bitLength="8">1</len>
                      <frameErrorFlag dataType="bit" bitLength="1">false</frameErrorFlag>
                      <bitErrorFlag dataType="bit" bitLength="1">false</bitErrorFlag>
                      <parityErrorFlag dataType="bit" bitLength="1">false</parityErrorFlag>
                      <unknownFlag dataType="bit" bitLength="1">false</unknownFlag>
                      <lostFlag dataType="bit" bitLength="1">false</lostFlag>
                      <sequenceNumber dataType="uint" bitLength="3">5</sequenceNumber>
                    </CEMIAdditionalInformationBusmonitorInfo>
                  </CEMIAdditionalInformation>
                  <CEMIAdditionalInformation>
                    <additionalInformationType dataType="uint" bitLength="8">4</additionalInformationType>
                    <CEMIAdditionalInformationRelativeTimestamp>
                      <len dataType="uint" bitLength="8">2</len>
                      <relativeTimestamp>
                        <RelativeTimestamp>
                          <timestamp dataType="uint" bitLength="16">17666</timestamp>
                        </RelativeTimestamp>
                      </relativeTimestamp>
                    </CEMIAdditionalInformationRelativeTimestamp>
                  </CEMIAdditionalInformation>
                </additionalInformation>
                <dataFrame>
                  <LDataFrame>
                    <frameType dataType="bit" bitLength="1">true</frameType>
                    <polling dataType="bit" bitLength="1">false</polling>
                    <notRepeated dataType="bit" bitLength="1">true</notRepeated>
                    <notAckFrame dataType="bit" bitLength="1">true</notAckFrame>
                    <priority>
                      <CEMIPriority dataType="uint" bitLength="2" stringRepresentation="LOW">3</CEMIPriority>
                    </priority>
                    <acknowledgeRequested dataType="bit" bitLength="1">false</acknowledgeRequested>
                    <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                    <LDataExtended>
                      <groupAddress dataType="bit" bitLength="1">false</groupAddress>
                      <hopCount dataType="uint" bitLength="3">3</hopCount>
                      <extendedFrameFormat dataType="uint" bitLength="4">6</extendedFrameFormat>
                      <sourceAddress>
                        <KnxAddress>
                          <mainGroup dataType="uint" bitLength="4">0</mainGroup>
                          <middleGroup dataType="uint" bitLength="4">10</middleGroup>
                          <subGroup dataType="uint" bitLength="8">30</subGroup>
                        </KnxAddress>
                      </sourceAddress>
                      <destinationAddress dataType="byte" bitLength="16">0x0ce1</destinationAddress>
                      <dataLength dataType="uint" bitLength="8">0</dataLength>
                      <apdu>
                        <Apdu>
                          <control dataType="uint" bitLength="1">1</control>
                          <numbered dataType="bit" bitLength="1">false</numbered>
                          <counter dataType="uint" bitLength="4">0</counter>
                          <ApduControlContainer>
                            <controlApdu>
                              <ApduControl>
                                <controlType dataType="uint" bitLength="2">1</controlType>
                                <ApduControlDisconnect>
                                </ApduControlDisconnect>
                              </ApduControl>
                            </controlApdu>
                          </ApduControlContainer>
                        </Apdu>
                      </apdu>
                    </LDataExtended>
                  </LDataFrame>
                </dataFrame>
                <crc dataType="uint" bitLength="8">13</crc>
              </LBusmonInd>
            </CEMI>
          </cemi>
        </TunnelingRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Tunneling Response</name>
    <raw>06100421000a046b0000</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">1057</msgType>
        <totalLength dataType="uint" bitLength="16">10</totalLength>
        <TunnelingResponse>
          <tunnelingResponseDataBlock>
            <TunnelingResponseDataBlock>
              <structureLength dataType="uint" bitLength="8">4</structureLength>
              <communicationChannelId dataType="uint" bitLength="8">107</communicationChannelId>
              <sequenceCounter dataType="uint" bitLength="8">0</sequenceCounter>
              <status>
                <Status dataType="uint" bitLength="8" stringRepresentation="NO_ERROR">0</Status>
              </status>
            </TunnelingResponseDataBlock>
          </tunnelingResponseDataBlock>
        </TunnelingResponse>
      </KnxNetIpMessage>
    </xml>
  </testcase>

  <testcase>
    <name>Default</name>
    <raw>0610020500180801c0a82a46c4090801c0a82a46c40a0203</raw>
    <root-type>KnxNetIpMessage</root-type>
    <xml>
      <KnxNetIpMessage>
        <headerLength dataType="uint" bitLength="8">6</headerLength>
        <protocolVersion dataType="uint" bitLength="8">16</protocolVersion>
        <msgType dataType="uint" bitLength="16">517</msgType>
        <totalLength dataType="uint" bitLength="16">24</totalLength>
        <ConnectionRequest>
          <hpaiDiscoveryEndpoint>
            <HPAIDiscoveryEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82a46</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">50185</ipPort>
            </HPAIDiscoveryEndpoint>
          </hpaiDiscoveryEndpoint>
          <hpaiDataEndpoint>
            <HPAIDataEndpoint>
              <structureLength dataType="uint" bitLength="8">8</structureLength>
              <hostProtocolCode>
                <HostProtocolCode dataType="uint" bitLength="8" stringRepresentation="IPV4_UDP">1</HostProtocolCode>
              </hostProtocolCode>
              <ipAddress>
                <IPAddress>
                  <addr dataType="byte" bitLength="32">0xc0a82a46</addr>
                </IPAddress>
              </ipAddress>
              <ipPort dataType="uint" bitLength="16">50186</ipPort>
            </HPAIDataEndpoint>
          </hpaiDataEndpoint>
          <connectionRequestInformation>
            <ConnectionRequestInformation>
              <structureLength dataType="uint" bitLength="8">2</structureLength>
              <connectionType dataType="uint" bitLength="8">3</connectionType>
              <ConnectionRequestInformationDeviceManagement>
              </ConnectionRequestInformationDeviceManagement>
            </ConnectionRequestInformation>
          </connectionRequestInformation>
        </ConnectionRequest>
      </KnxNetIpMessage>
    </xml>
  </testcase>

</test:testsuite>
