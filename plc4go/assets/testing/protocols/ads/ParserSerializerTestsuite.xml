<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:testsuite xmlns:test="https://plc4x.apache.org/schemas/parser-serializer-testsuite.xsd"
                byteOrder="LITTLE_ENDIAN">

  <name><PERSON>hoff ADS/AMS</name>

  <protocolName>ads</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <testcase>
    <name>Ams-Single-Item-Read-Request</name>
    <raw>00002c000000c0a8171401015303c0a817c801015303020004000c000000000000000200000005f000000000801a01000000</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">200</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">2</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61445</indexGroup>
              <indexOffset dataType="uint" bitLength="32">444596224</indexOffset>
              <length dataType="uint" bitLength="32">1</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Single-Item-Read-Response</name>
    <raw>000029000000c0a817c801015303c0a817140101530302000500090000000000000002000000000000000100000001</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">41</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">200</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">9</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">2</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">1</length>
              <data dataType="byte" bitLength="8">0x01</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Resolve-Symbolic-Address-Request</name>
    <raw>00004a000000c0a8171401015303c0a817cd0101feff090004002a000000000000000100000003f0000000000000040000001a0000006d61696e2e665f74726967446174656947656c6573656e2e4d00</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">74</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">42</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadWriteRequest>
              <indexGroup dataType="uint" bitLength="32">61443</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <readLength dataType="uint" bitLength="32">4</readLength>
              <writeLength dataType="uint" bitLength="32">26</writeLength>
              <items isList="true">
              </items>
              <data dataType="byte" bitLength="208">0x6d61696e2e665f74726967446174656947656c6573656e2e4d00</data>
            </AdsReadWriteRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Resolve-Symbolic-Address-Response</name>
    <raw>00002c000000c0a817cd0101feffc0a8171401015303090005000c000000000000000100000000000000040000000100801b</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadWriteResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">4</length>
              <data dataType="byte" bitLength="32">0x0100801b</data>
            </AdsReadWriteResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Read-Symbolic-Address-Request</name>
    <raw>00002c000000c0a8171401015303c0a817cd0101feff020004000c000000000000000100000005f000000100801b04000000</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61445</indexGroup>
              <indexOffset dataType="uint" bitLength="32">461373441</indexOffset>
              <length dataType="uint" bitLength="32">4</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Read-Symbolic-Address-Response</name>
    <raw>000029000000c0a817cd0101feffc0a817140101530302000500090000000000000001000000000000000100000001</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">41</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">9</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">1</length>
              <data dataType="byte" bitLength="8">0x01</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Release-Symbolic-Address-Handle-Request</name>
    <raw>000030000000c0a8171401015303c0a817cd0101feff0300040010000000000000000100000006f0000000000000040000000100801b</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">48</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_WRITE">3</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">16</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsWriteRequest>
              <indexGroup dataType="uint" bitLength="32">61446</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">4</length>
              <data dataType="byte" bitLength="32">0x0100801b</data>
            </AdsWriteRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

  <testcase>
    <name>Ams-Release-Symbolic-Address-Handle-Response</name>
    <raw>000024000000c0a817cd0101feffc0a81714010153030300050004000000000000000100000000000000</raw>
    <root-type>AmsTCPPacket</root-type>
    <xml>
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">36</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">205</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_WRITE">3</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">4</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsWriteResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
            </AdsWriteResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </xml>
  </testcase>

</test:testsuite>