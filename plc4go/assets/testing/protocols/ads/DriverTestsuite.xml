<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:driver-testsuite xmlns:test="https://plc4x.apache.org/schemas/driver-testsuite.xsd"
                       byteOrder="LITTLE_ENDIAN">

  <name><PERSON>hoff ADS/AMS</name>

  <protocolName>ads</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <driver-name>ads</driver-name>
  <driver-parameters>
    <parameter>
      <name>source-ams-net-id</name>
      <value>**************.1.1</value>
    </parameter>
    <parameter>
      <name>source-ams-port</name>
      <value>65534</value>
    </parameter>
    <parameter>
      <name>target-ams-net-id</name>
      <value>192.168.23.20.1.1</value>
    </parameter>
    <parameter>
      <name>target-ams-port</name>
      <value>851</value>
    </parameter>
  </driver-parameters>

  <setup>
    <!-- Read the Device Info from the remote device -->
    <outgoing-plc-message name="Read Device Info Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">32</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">800</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_DEVICE_INFO">1</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">0</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadDeviceInfoRequest>
            </AdsReadDeviceInfoRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read Device Info Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">56</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">800</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_DEVICE_INFO">1</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">24</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">1</invokeId>
            <AdsReadDeviceInfoResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <majorVersion dataType="uint" bitLength="8">3</majorVersion>
              <minorVersion dataType="uint" bitLength="8">1</minorVersion>
              <version dataType="uint" bitLength="16">1926</version>
              <device dataType="byte" bitLength="128">0x506c6333302041707000000000000000</device>
            </AdsReadDeviceInfoResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!--
      Next read the "online version number" to know the version of any "online-changes"
      (This is usually 0 unless "online changes" have been deployed by the Engineering environment)
    -->
    <outgoing-plc-message name="Read 'online version-number' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">98</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">800</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">66</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">2</invokeId>
            <AdsReadWriteRequest>
              <indexGroup dataType="uint" bitLength="32">61444</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <readLength dataType="uint" bitLength="32">4</readLength>
              <writeLength dataType="uint" bitLength="32">50</writeLength>
              <items isList="true">
              </items>
              <data dataType="byte" bitLength="400">0x5477696e4341545f53797374656d496e666f5661724c6973742e5f417070496e666f2e4f6e6c696e654368616e6765436e74</data>
            </AdsReadWriteRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read 'online version-number' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">800</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">2</invokeId>
            <AdsReadWriteResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">4</length>
              <data dataType="byte" bitLength="32">0x00000000</data>
            </AdsReadWriteResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!--
      Now read the offline version number
      (This is an ever-increasing counter of how often the plc programm has been updated)
    -->
    <outgoing-plc-message name="Read 'offline version-number' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">800</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">3</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61448</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">1</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read 'offline version-number' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">41</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">800</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">9</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">3</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">1</length>
              <data dataType="byte" bitLength="8">0xd7</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!-- Read the size of the symbol table and datatype tables -->
    <outgoing-plc-message name="Read 'size of symbol- and datatype table' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">4</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61455</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">24</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read 'size of symbol- and datatype table' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">64</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">32</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">4</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">24</length>
              <data dataType="byte" bitLength="192">0x7601000050a400006c000000608c0000d007000000000000</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!-- Load the datatype table information -->
    <outgoing-plc-message name="Read 'datatype table' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">5</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61454</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">35936</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read 'datatype table' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">35976</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">35944</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">5</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">35936</length>
              <data dataType="byte" bitLength="287488">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</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!-- Read the symbol-table -->
    <outgoing-plc-message name="Read 'symbol table' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">44</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">12</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">6</invokeId>
            <AdsReadRequest>
              <indexGroup dataType="uint" bitLength="32">61451</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">42064</length>
            </AdsReadRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Read 'symbol table' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">42104</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">42072</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">6</invokeId>
            <AdsReadResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <length dataType="uint" bitLength="32">42064</length>
              <data dataType="byte" bitLength="336512">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</data>
            </AdsReadResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!--
      Subscribe to changes in the "online-version" number
      (So we can detect plc programm updates while being connected)
    -->
    <outgoing-plc-message name="Subscribe 'online-version' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">72</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_ADD_DEVICE_NOTIFICATION">6</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">40</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">7</invokeId>
            <AdsAddDeviceNotificationRequest>
              <indexGroup dataType="uint" bitLength="32">16448</indexGroup>
              <indexOffset dataType="uint" bitLength="32">919256</indexOffset>
              <length dataType="uint" bitLength="32">4</length>
              <transmissionMode>
                <AdsTransMode dataType="uint" bitLength="32" stringRepresentation="ON_CHANGE">4</AdsTransMode>
              </transmissionMode>
              <maxDelayInMs dataType="uint" bitLength="32">0</maxDelayInMs>
              <cycleTimeInMs dataType="uint" bitLength="32">1000</cycleTimeInMs>
              <reserved dataType="uint" bitLength="64">0</reserved>
              <reserved dataType="uint" bitLength="64">0</reserved>
            </AdsAddDeviceNotificationRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Subscribe 'online-version' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">40</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_ADD_DEVICE_NOTIFICATION">6</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">8</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">7</invokeId>
            <AdsAddDeviceNotificationResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <notificationHandle dataType="uint" bitLength="32">25</notificationHandle>
            </AdsAddDeviceNotificationResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!--
      Subscribe to changes in the "offline-version" number
      (So we can detect plc programm updates while being connected)
    -->
    <outgoing-plc-message name="Subscribe 'offline-version' Request">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">72</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_ADD_DEVICE_NOTIFICATION">6</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">40</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">8</invokeId>
            <AdsAddDeviceNotificationRequest>
              <indexGroup dataType="uint" bitLength="32">61448</indexGroup>
              <indexOffset dataType="uint" bitLength="32">0</indexOffset>
              <length dataType="uint" bitLength="32">1</length>
              <transmissionMode>
                <AdsTransMode dataType="uint" bitLength="32" stringRepresentation="ON_CHANGE">4</AdsTransMode>
              </transmissionMode>
              <maxDelayInMs dataType="uint" bitLength="32">0</maxDelayInMs>
              <cycleTimeInMs dataType="uint" bitLength="32">1000</cycleTimeInMs>
              <reserved dataType="uint" bitLength="64">0</reserved>
              <reserved dataType="uint" bitLength="64">0</reserved>
            </AdsAddDeviceNotificationRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </outgoing-plc-message>
    <incoming-plc-message name="Subscribe 'offline-version' Response">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">40</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_ADD_DEVICE_NOTIFICATION">6</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">true</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">8</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">8</invokeId>
            <AdsAddDeviceNotificationResponse>
              <result>
                <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
              </result>
              <notificationHandle dataType="uint" bitLength="32">26</notificationHandle>
            </AdsAddDeviceNotificationResponse>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
    <!--
      After subscribing to an address the PLC usually sends back the current values
      (As we've also just manually read these, they will match the ones we read and
      no action will be taken by the driver)
    -->
    <incoming-plc-message name="Subscription 'offline- and online-version' Notification">
      <AmsTCPPacket>
        <reserved dataType="uint" bitLength="16">0</reserved>
        <length dataType="uint" bitLength="32">73</length>
        <userdata>
          <AmsPacket>
            <targetAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">220</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </targetAmsNetId>
            <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
            <sourceAmsNetId>
              <AmsNetId>
                <octet1 dataType="uint" bitLength="8">192</octet1>
                <octet2 dataType="uint" bitLength="8">168</octet2>
                <octet3 dataType="uint" bitLength="8">23</octet3>
                <octet4 dataType="uint" bitLength="8">20</octet4>
                <octet5 dataType="uint" bitLength="8">1</octet5>
                <octet6 dataType="uint" bitLength="8">1</octet6>
              </AmsNetId>
            </sourceAmsNetId>
            <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
            <commandId>
              <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_DEVICE_NOTIFICATION">8</CommandId>
            </commandId>
            <initCommand dataType="bit" bitLength="1">false</initCommand>
            <updCommand dataType="bit" bitLength="1">false</updCommand>
            <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
            <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
            <systemCommand dataType="bit" bitLength="1">false</systemCommand>
            <adsCommand dataType="bit" bitLength="1">true</adsCommand>
            <noReturn dataType="bit" bitLength="1">false</noReturn>
            <response dataType="bit" bitLength="1">false</response>
            <broadcast dataType="bit" bitLength="1">false</broadcast>
            <reserved dataType="int" bitLength="7">0</reserved>
            <length dataType="uint" bitLength="32">41</length>
            <errorCode dataType="uint" bitLength="32">0</errorCode>
            <invokeId dataType="uint" bitLength="32">0</invokeId>
            <AdsDeviceNotificationRequest>
              <length dataType="uint" bitLength="32">37</length>
              <stamps dataType="uint" bitLength="32">1</stamps>
              <adsStampHeaders isList="true">
                <AdsStampHeader>
                  <timestamp dataType="uint" bitLength="64">133519238331750000</timestamp>
                  <samples dataType="uint" bitLength="32">2</samples>
                  <adsNotificationSamples isList="true">
                    <AdsNotificationSample>
                      <notificationHandle dataType="uint" bitLength="32">25</notificationHandle>
                      <sampleSize dataType="uint" bitLength="32">4</sampleSize>
                      <data dataType="byte" bitLength="32">0x00000000</data>
                    </AdsNotificationSample>
                    <AdsNotificationSample>
                      <notificationHandle dataType="uint" bitLength="32">26</notificationHandle>
                      <sampleSize dataType="uint" bitLength="32">1</sampleSize>
                      <data dataType="byte" bitLength="8">0xd7</data>
                    </AdsNotificationSample>
                  </adsNotificationSamples>
                </AdsStampHeader>
              </adsStampHeaders>
            </AdsDeviceNotificationRequest>
          </AmsPacket>
        </userdata>
      </AmsTCPPacket>
    </incoming-plc-message>
  </setup>

  <testcase>
    <name>Single element read request for "Struct" type field</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz</name>
              <address>MAIN.hurz_Struct</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Ads Read Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">220</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">9</invokeId>
              <AdsReadRequest>
                <indexGroup dataType="uint" bitLength="32">16448</indexGroup>
                <indexOffset dataType="uint" bitLength="32">417176</indexOffset>
                <length dataType="uint" bitLength="32">336</length>
              </AdsReadRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Read Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">376</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">220</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">344</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">9</invokeId>
              <AdsReadResponse>
                <result>
                  <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
                </result>
                <length dataType="uint" bitLength="32">336</length>
                <data dataType="byte" bitLength="2688">0x010102000300000004000000000000000506070008000900090000000a0000000b000000000000000c0000000000000000005041000000000000000000002c40d2040000330100a07c941a93b826330100a07d0fab6459037e1c8e316875727a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007700000000000000000077006f006c00660000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008945f0ebbf585e5f590000</data>
              </AdsReadResponse>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <PlcTagItem>
                      <tag>
                        <SymbolicAdsTag>
                          <symbolicAddress dataType="string" bitLength="128" encoding="UTF-8">MAIN.hurz_Struct</symbolicAddress>
                        </SymbolicAdsTag>
                      </tag>
                    </PlcTagItem>
                  </hurz>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcStruct>
                    <hurz_STRING>
                      <PlcSTRING dataType="string" bitLength="32" encoding="UTF-8">hurz</PlcSTRING>
                    </hurz_STRING>
                    <hurz_LTIME>
                      <PlcLTIME dataType="string" bitLength="192" encoding="UTF-8">PT24015H23M12.034002044S</PlcLTIME>
                    </hurz_LTIME>
                    <hurz_LWORD>
                      <PlcLWORD dataType="int" bitLength="64">4</PlcLWORD>
                    </hurz_LWORD>
                    <hurz_REAL>
                      <PlcREAL dataType="float" bitLength="32">13.0</PlcREAL>
                    </hurz_REAL>
                    <hurz_ULINT>
                      <PlcULINT dataType="int" bitLength="64">12</PlcULINT>
                    </hurz_ULINT>
                    <hurz_WSTRING>
                      <PlcWSTRING dataType="string" bitLength="80" encoding="UTF-8">wolf</PlcWSTRING>
                    </hurz_WSTRING>
                    <hurz_LINT>
                      <PlcLINT dataType="int" bitLength="64">11</PlcLINT>
                    </hurz_LINT>
                    <hurz_DATE_AND_TIME>
                      <PlcDATE_AND_TIME dataType="string" bitLength="152" encoding="UTF-8">1996-05-06T15:36:30</PlcDATE_AND_TIME>
                    </hurz_DATE_AND_TIME>
                    <hurz_UINT>
                      <PlcUINT dataType="int" bitLength="16">8</PlcUINT>
                    </hurz_UINT>
                    <hurz_SINT>
                      <PlcSINT dataType="int" bitLength="8">5</PlcSINT>
                    </hurz_SINT>
                    <hurz_USINT>
                      <PlcUSINT dataType="int" bitLength="8">6</PlcUSINT>
                    </hurz_USINT>
                    <hurz_LREAL>
                      <PlcLREAL dataType="float" bitLength="64">14.0</PlcLREAL>
                    </hurz_LREAL>
                    <hurz_TIME_OF_DAY>
                      <PlcTIME_OF_DAY dataType="string" bitLength="96" encoding="UTF-8">15:36:30.123</PlcTIME_OF_DAY>
                    </hurz_TIME_OF_DAY>
                    <hurz_BOOL>
                      <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                    </hurz_BOOL>
                    <hurz_DINT>
                      <PlcDINT dataType="int" bitLength="32">9</PlcDINT>
                    </hurz_DINT>
                    <hurz_UDINT>
                      <PlcUDINT dataType="int" bitLength="32">10</PlcUDINT>
                    </hurz_UDINT>
                    <hurz_BYTE>
                      <PlcBYTE dataType="int" bitLength="8">1</PlcBYTE>
                    </hurz_BYTE>
                    <hurz_TIME>
                      <PlcTIME dataType="int" bitLength="32">1234</PlcTIME>
                    </hurz_TIME>
                    <hurz_DWORD>
                      <PlcDWORD dataType="int" bitLength="32">3</PlcDWORD>
                    </hurz_DWORD>
                    <hurz_INT>
                      <PlcINT dataType="int" bitLength="16">7</PlcINT>
                    </hurz_INT>
                    <hurz_WORD>
                      <PlcWORD dataType="int" bitLength="16">2</PlcWORD>
                    </hurz_WORD>
                    <hurz_DATE>
                      <PlcDATE dataType="string" bitLength="80" encoding="UTF-8">1978-03-28</PlcDATE>
                    </hurz_DATE>
                  </PlcStruct>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
          <metadata isList="true">
            <hurz>
              <entry>
                <key dataType="string" bitLength="17" encoding="UTF-8">receive_timestamp</key>
                <value dataType="string" bitLength="13" encoding="UTF-8" plc4x-skip-comparison="true">0</value>
              </entry>
              <entry>
                <key dataType="string" bitLength="16" encoding="UTF-8">timestamp_source</key>
                <value dataType="string" bitLength="10" encoding="UTF-8">ASSUMPTION</value>
              </entry>
            </hurz>
          </metadata>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Single element write request for "Struct" type field</name>
    <steps>
      <api-request name="Receive Write Request from application">
        <TestWriteRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestValueTag">
              <name>hurz</name>
              <address>MAIN.hurz_Struct</address>
              <value>
                <PlcStruct>
                  <hurz_STRING>
                    <PlcSTRING dataType="string" bitLength="32" encoding="UTF-8">hurz</PlcSTRING>
                  </hurz_STRING>
                  <hurz_LTIME>
                    <PlcLTIME dataType="string" bitLength="192" encoding="UTF-8">PT24015H23M12.034002044S</PlcLTIME>
                  </hurz_LTIME>
                  <hurz_LWORD>
                    <PlcLWORD dataType="int" bitLength="64">4</PlcLWORD>
                  </hurz_LWORD>
                  <hurz_REAL>
                    <PlcREAL dataType="float" bitLength="32">13.0</PlcREAL>
                  </hurz_REAL>
                  <hurz_ULINT>
                    <PlcULINT dataType="int" bitLength="64">12</PlcULINT>
                  </hurz_ULINT>
                  <hurz_WSTRING>
                    <PlcWSTRING dataType="string" bitLength="80" encoding="UTF-8">wolf</PlcWSTRING>
                  </hurz_WSTRING>
                  <hurz_LINT>
                    <PlcLINT dataType="int" bitLength="64">11</PlcLINT>
                  </hurz_LINT>
                  <hurz_DATE_AND_TIME>
                    <PlcDATE_AND_TIME dataType="string" bitLength="152" encoding="UTF-8">1996-05-06T15:36:30</PlcDATE_AND_TIME>
                  </hurz_DATE_AND_TIME>
                  <hurz_UINT>
                    <PlcUINT dataType="int" bitLength="16">8</PlcUINT>
                  </hurz_UINT>
                  <hurz_SINT>
                    <PlcSINT dataType="int" bitLength="8">5</PlcSINT>
                  </hurz_SINT>
                  <hurz_USINT>
                    <PlcUSINT dataType="int" bitLength="8">6</PlcUSINT>
                  </hurz_USINT>
                  <hurz_LREAL>
                    <PlcLREAL dataType="float" bitLength="64">14.0</PlcLREAL>
                  </hurz_LREAL>
                  <hurz_TIME_OF_DAY>
                    <PlcTIME_OF_DAY dataType="string" bitLength="96" encoding="UTF-8">15:36:30.123</PlcTIME_OF_DAY>
                  </hurz_TIME_OF_DAY>
                  <hurz_BOOL>
                    <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                  </hurz_BOOL>
                  <hurz_DINT>
                    <PlcDINT dataType="int" bitLength="32">9</PlcDINT>
                  </hurz_DINT>
                  <hurz_UDINT>
                    <PlcUDINT dataType="int" bitLength="32">10</PlcUDINT>
                  </hurz_UDINT>
                  <hurz_BYTE>
                    <PlcBYTE dataType="int" bitLength="8">1</PlcBYTE>
                  </hurz_BYTE>
                  <hurz_TIME>
                    <PlcTIME dataType="int" bitLength="32">1234</PlcTIME>
                  </hurz_TIME>
                  <hurz_DWORD>
                    <PlcDWORD dataType="int" bitLength="32">3</PlcDWORD>
                  </hurz_DWORD>
                  <hurz_INT>
                    <PlcINT dataType="int" bitLength="16">7</PlcINT>
                  </hurz_INT>
                  <hurz_WORD>
                    <PlcWORD dataType="int" bitLength="16">2</PlcWORD>
                  </hurz_WORD>
                  <hurz_DATE>
                    <PlcDATE dataType="string" bitLength="80" encoding="UTF-8">1978-03-28</PlcDATE>
                  </hurz_DATE>
                </PlcStruct>
              </value>
            </tag>
          </tags>
        </TestWriteRequest>
      </api-request>
      <outgoing-plc-message name="Send Ads Write Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">380</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">851</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">220</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">65534</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_WRITE">3</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">348</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">9</invokeId>
              <AdsWriteRequest>
                <indexGroup dataType="uint" bitLength="32">16448</indexGroup>
                <indexOffset dataType="uint" bitLength="32">417176</indexOffset>
                <length dataType="uint" bitLength="32">336</length>
                <data dataType="byte" bitLength="2688">0x010102000300000004000000000000000506070008000000090000000a0000000b000000000000000c0000000000000000005041000000000000000000002c40d2040000000000007c941a93b826330100a07d0fab6459037e1c8e316875727a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000077006f006c00660000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000</data>
              </AdsWriteRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message>
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">36</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">220</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">65534</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">851</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_WRITE">3</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">4</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">10</invokeId>
              <AdsWriteResponse>
                <result>
                  <ReturnCode dataType="uint" bitLength="32" stringRepresentation="OK">0</ReturnCode>
                </result>
              </AdsWriteResponse>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
    </steps>
  </testcase>

  <!--testcase>
    <name>Single element direct read request</name>
    <description>
      When doing a simple read request with only a single direct address, the
      request should be answered directly.
    </description>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz</name>
              <address>00004040/00000008:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Ads Read Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <AdsReadRequest>
                <indexGroup dataType="uint" bitLength="32">61455</indexGroup>
                <indexOffset dataType="uint" bitLength="32">0</indexOffset>
                <length dataType="uint" bitLength="32">24</length>
              </AdsReadRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Ads Read Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">41</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">9</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <data>
                <AdsData>
                  <AdsReadResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">1</length>
                    <data dataType="byte" bitLength="8">0x01</data>
                  </AdsReadResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <PlcReadRequest>
            <PlcTagRequest>
              <tags isList="true">
                <hurz>
                  <DirectAdsField>
                    <indexGroup dataType="uint" bitLength="32">4040</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">8</indexOffset>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </DirectAdsField>
                </hurz>
              </tags>
            </PlcTagRequest>
          </PlcReadRequest>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
        </PlcReadResponse>
      </api-response>
      <delay>1000</delay>
    </steps>
  </testcase>

  <testcase>
    <name>Multi-element direct read request</name>
    <description>
      When doing a simple read request with only direct addresses, but multiple
      ones, the unofficial multi-read method should be used to read all in one go.
    </description>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz1</name>
              <address>00004040/00000008:BOOL</address>
            </tag>
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz2</name>
              <address>00004040/00000012:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Ads Read Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <AdsReadRequest>
                <indexGroup dataType="uint" bitLength="32">61455</indexGroup>
                <indexOffset dataType="uint" bitLength="32">0</indexOffset>
                <length dataType="uint" bitLength="32">24</length>
              </AdsReadRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>

      </outgoing-plc-message>
      <incoming-plc-message name="Receive Ads Read Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">50</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">18</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <data>
                <AdsData>
                  <AdsReadWriteResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">10</length>
                    <data dataType="byte" bitLength="80">0x00000000000000000101</data>
                  </AdsReadWriteResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <PlcReadRequest>
            <PlcTagRequest>
              <tags isList="true">
                <hurz1>
                  <DirectAdsField>
                    <indexGroup dataType="uint" bitLength="32">4040</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">8</indexOffset>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </DirectAdsField>
                </hurz1>
                <hurz2>
                  <DirectAdsField>
                    <indexGroup dataType="uint" bitLength="32">4040</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">12</indexOffset>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </DirectAdsField>
                </hurz2>
              </tags>
            </PlcTagRequest>
          </PlcReadRequest>
          <values isList="true">
            <hurz1>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz1>
            <hurz2>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz2>
          </values>
        </PlcReadResponse>
      </api-response>
      <delay>1000</delay>
    </steps>
  </testcase>

  <testcase>
    <name>Single element symbolic read request</name>
    <description>
      When doing a simple read request with only a single symbolic address, which has not
      been resolved previously, first a resolution request has to be issued and the data
      from the response should be used in a second request to actually read the data.
    </description>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz1</name>
              <address>main.f_trigDateiGelesen.M:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Resolve Symbolic Address Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <AdsReadRequest>
                <indexGroup dataType="uint" bitLength="32">61455</indexGroup>
                <indexOffset dataType="uint" bitLength="32">0</indexOffset>
                <length dataType="uint" bitLength="32">24</length>
              </AdsReadRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Resolve Symbolic Address Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <data>
                <AdsData>
                  <AdsReadWriteResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">4</length>
                    <data dataType="byte" bitLength="32">0x0100801b</data>
                  </AdsReadWriteResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <outgoing-plc-message name="Send Ads Read Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">2</invokeId>
              <data>
                <AdsData>
                  <AdsReadRequest>
                    <indexGroup dataType="uint" bitLength="32">61445</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">461373441</indexOffset>
                    <length dataType="uint" bitLength="32">1</length>
                  </AdsReadRequest>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Ads Read Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">50</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">18</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">2</invokeId>
              <data>
                <AdsData>
                  <AdsReadResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">10</length>
                    <data dataType="byte" bitLength="80">0x00000000000000000101</data>
                  </AdsReadResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <PlcReadRequest>
            <PlcTagRequest>
              <tags isList="true">
                <hurz1>
                  <SymbolicAdsField>
                    <symbolicAddress dataType="string" bitLength="200" encoding="UTF-8">main.f_trigDateiGelesen.M
                    </symbolicAddress>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </SymbolicAdsField>
                </hurz1>
              </tags>
            </PlcTagRequest>
          </PlcReadRequest>
          <values isList="true">
            <hurz1>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">false</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz1>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Single element symbolic read request (Address previously resolved)</name>
    <description>
      When doing a simple read request with only a single symbolic address, which has
      been resolved previously, the data from the previous request should be used directly
      without re-resolving it.
    </description>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz1</name>
              <address>main.f_trigDateiGelesen.M:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Resolve Symbolic Address Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <AdsReadRequest>
                <indexGroup dataType="uint" bitLength="32">61455</indexGroup>
                <indexOffset dataType="uint" bitLength="32">0</indexOffset>
                <length dataType="uint" bitLength="32">24</length>
              </AdsReadRequest>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Resolve Symbolic Address Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ_WRITE">9</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">1</invokeId>
              <data>
                <AdsData>
                  <AdsReadWriteResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">4</length>
                    <data dataType="byte" bitLength="32">0x0100801b</data>
                  </AdsReadWriteResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <outgoing-plc-message name="Send Ads Read Request">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">2</invokeId>
              <data>
                <AdsData>
                  <AdsReadRequest>
                    <indexGroup dataType="uint" bitLength="32">61445</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">461373441</indexOffset>
                    <length dataType="uint" bitLength="32">1</length>
                  </AdsReadRequest>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>

      </outgoing-plc-message>
      <incoming-plc-message name="Receive Ads Read Response">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">50</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">18</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">2</invokeId>
              <data>
                <AdsData>
                  <AdsReadResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">10</length>
                    <data dataType="byte" bitLength="80">0x00000000000000000101</data>
                  </AdsReadResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>

      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <PlcReadRequest>
            <PlcTagRequest>
              <tags isList="true">
                <hurz1>
                  <SymbolicAdsField>
                    <symbolicAddress dataType="string" bitLength="200" encoding="UTF-8">main.f_trigDateiGelesen.M
                    </symbolicAddress>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </SymbolicAdsField>
                </hurz1>
              </tags>
            </PlcTagRequest>
          </PlcReadRequest>
          <values isList="true">
            <hurz1>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">false</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz1>
          </values>
        </PlcReadResponse>
      </api-response>
      <delay>500</delay>
      <api-request name="Receive a second Read Request for the same resource from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz1</name>
              <address>main.f_trigDateiGelesen.M:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Ads Read Request directly using the preciously resolved address">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">44</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">false</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">12</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">3</invokeId>
              <data>
                <AdsData>
                  <AdsReadRequest>
                    <indexGroup dataType="uint" bitLength="32">61445</indexGroup>
                    <indexOffset dataType="uint" bitLength="32">461373441</indexOffset>
                    <length dataType="uint" bitLength="32">1</length>
                  </AdsReadRequest>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Ads Read Response again">
        <AmsTCPPacket>
          <reserved dataType="uint" bitLength="16">0</reserved>
          <length dataType="uint" bitLength="32">50</length>
          <userdata>
            <AmsPacket>
              <targetAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">200</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </targetAmsNetId>
              <targetAmsPort dataType="uint" bitLength="16">48898</targetAmsPort>
              <sourceAmsNetId>
                <AmsNetId>
                  <octet1 dataType="uint" bitLength="8">192</octet1>
                  <octet2 dataType="uint" bitLength="8">168</octet2>
                  <octet3 dataType="uint" bitLength="8">23</octet3>
                  <octet4 dataType="uint" bitLength="8">20</octet4>
                  <octet5 dataType="uint" bitLength="8">1</octet5>
                  <octet6 dataType="uint" bitLength="8">1</octet6>
                </AmsNetId>
              </sourceAmsNetId>
              <sourceAmsPort dataType="uint" bitLength="16">48898</sourceAmsPort>
              <commandId>
                <CommandId dataType="uint" bitLength="16" stringRepresentation="ADS_READ">2</CommandId>
              </commandId>
              <initCommand dataType="bit" bitLength="1">false</initCommand>
              <updCommand dataType="bit" bitLength="1">false</updCommand>
              <timestampAdded dataType="bit" bitLength="1">false</timestampAdded>
              <highPriorityCommand dataType="bit" bitLength="1">false</highPriorityCommand>
              <systemCommand dataType="bit" bitLength="1">false</systemCommand>
              <adsCommand dataType="bit" bitLength="1">true</adsCommand>
              <noReturn dataType="bit" bitLength="1">false</noReturn>
              <response dataType="bit" bitLength="1">true</response>
              <broadcast dataType="bit" bitLength="1">false</broadcast>
              <reserved dataType="int" bitLength="7">0</reserved>
              <length dataType="uint" bitLength="32">18</length>
              <errorCode dataType="uint" bitLength="32">0</errorCode>
              <invokeId dataType="uint" bitLength="32">3</invokeId>
              <data>
                <AdsData>
                  <AdsReadResponse>
                    <result>
                      <ReturnCode dataType="uint" bitLength="32">0</ReturnCode>
                    </result>
                    <length dataType="uint" bitLength="32">10</length>
                    <data dataType="byte" bitLength="80">0x00000000000000000101</data>
                  </AdsReadResponse>
                </AdsData>
              </data>
            </AmsPacket>
          </userdata>
        </AmsTCPPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application again">
        <PlcReadResponse>
          <PlcReadRequest>
            <PlcTagRequest>
              <tags isList="true">
                <hurz1>
                  <SymbolicAdsField>
                    <symbolicAddress dataType="string" bitLength="200" encoding="UTF-8">main.f_trigDateiGelesen.M
                    </symbolicAddress>
                    <numberOfElements dataType="uint" bitLength="32">1</numberOfElements>
                    <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                  </SymbolicAdsField>
                </hurz1>
              </tags>
            </PlcTagRequest>
          </PlcReadRequest>
          <values isList="true">
            <hurz1>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">false</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz1>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase-->

</test:driver-testsuite>
