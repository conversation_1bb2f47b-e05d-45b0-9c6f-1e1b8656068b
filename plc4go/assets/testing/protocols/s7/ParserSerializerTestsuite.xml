<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:testsuite xmlns:test="https://plc4x.apache.org/schemas/parser-serializer-testsuite.xsd"
                byteOrder="BIG_ENDIAN">

  <name>S7</name>

  <protocolName>s7</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <testcase>
    <name>COTP Connection Request</name>
    <raw>0300001611e00000000f00c2020100c1020311c0010a</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">22</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">17</headerLength>
            <tpduCode dataType="uint" bitLength="8">224</tpduCode>
            <COTPPacketConnectionRequest>
              <destinationReference dataType="uint" bitLength="16">0</destinationReference>
              <sourceReference dataType="uint" bitLength="16">15</sourceReference>
              <protocolClass>
                <COTPProtocolClass dataType="uint" bitLength="8" stringRepresentation="CLASS_0">0</COTPProtocolClass>
              </protocolClass>
            </COTPPacketConnectionRequest>
            <parameters isList="true">
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">194</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCalledTsap>
                  <tsapId dataType="uint" bitLength="16">256</tsapId>
                </COTPParameterCalledTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">193</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCallingTsap>
                  <tsapId dataType="uint" bitLength="16">785</tsapId>
                </COTPParameterCallingTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">192</parameterType>
                <parameterLength dataType="uint" bitLength="8">1</parameterLength>
                <COTPParameterTpduSize>
                  <tpduSize>
                    <COTPTpduSize dataType="uint" bitLength="8" stringRepresentation="SIZE_1024">10</COTPTpduSize>
                  </tpduSize>
                </COTPParameterTpduSize>
              </COTPParameter>
            </parameters>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>COTP Connection Response</name>
    <raw>0300001611d0000f000b00c0010ac1020311c2020100</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">22</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">17</headerLength>
            <tpduCode dataType="uint" bitLength="8">208</tpduCode>
            <COTPPacketConnectionResponse>
              <destinationReference dataType="uint" bitLength="16">15</destinationReference>
              <sourceReference dataType="uint" bitLength="16">11</sourceReference>
              <protocolClass>
                <COTPProtocolClass dataType="uint" bitLength="8" stringRepresentation="CLASS_0">0</COTPProtocolClass>
              </protocolClass>
            </COTPPacketConnectionResponse>
            <parameters isList="true">
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">192</parameterType>
                <parameterLength dataType="uint" bitLength="8">1</parameterLength>
                <COTPParameterTpduSize>
                  <tpduSize>
                    <COTPTpduSize dataType="uint" bitLength="8" stringRepresentation="SIZE_1024">10</COTPTpduSize>
                  </tpduSize>
                </COTPParameterTpduSize>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">193</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCallingTsap>
                  <tsapId dataType="uint" bitLength="16">785</tsapId>
                </COTPParameterCallingTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">194</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCalledTsap>
                  <tsapId dataType="uint" bitLength="16">256</tsapId>
                </COTPParameterCalledTsap>
              </COTPParameter>
            </parameters>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Setup Communication Request</name>
    <raw>0300001902f08132010000000000080000f0000008000803f0</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">25</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">1</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">1</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">0</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageRequest>
                </S7MessageRequest>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">240</parameterType>
                    <S7ParameterSetupCommunication>
                      <reserved dataType="uint" bitLength="8">0</reserved>
                      <maxAmqCaller dataType="uint" bitLength="16">8</maxAmqCaller>
                      <maxAmqCallee dataType="uint" bitLength="16">8</maxAmqCallee>
                      <pduLength dataType="uint" bitLength="16">1008</pduLength>
                    </S7ParameterSetupCommunication>
                  </S7Parameter>
                </parameter>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Setup Communication Response</name>
    <raw>0300001b02f080320300000000000800000000f0000003000300f0</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">27</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">3</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">0</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageResponseData>
                  <errorClass dataType="uint" bitLength="8">0</errorClass>
                  <errorCode dataType="uint" bitLength="8">0</errorCode>
                </S7MessageResponseData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">240</parameterType>
                    <S7ParameterSetupCommunication>
                      <reserved dataType="uint" bitLength="8">0</reserved>
                      <maxAmqCaller dataType="uint" bitLength="16">3</maxAmqCaller>
                      <maxAmqCallee dataType="uint" bitLength="16">3</maxAmqCallee>
                      <pduLength dataType="uint" bitLength="16">240</pduLength>
                    </S7ParameterSetupCommunication>
                  </S7Parameter>
                </parameter>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Read PLC Type Request</name>
    <raw>0300002102f082320700000001000800080001120411440100ff09000400110000</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">33</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">2</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">7</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">1</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">8</payloadLength>
                <S7MessageUserData>
                </S7MessageUserData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">0</parameterType>
                    <S7ParameterUserData>
                      <numItems dataType="uint" bitLength="8">1</numItems>
                      <items isList="true">
                        <S7ParameterUserDataItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7ParameterUserDataItemCPUFunctions>
                            <itemLength dataType="uint" bitLength="8">4</itemLength>
                            <method dataType="uint" bitLength="8">17</method>
                            <cpuFunctionType dataType="uint" bitLength="4">4</cpuFunctionType>
                            <cpuFunctionGroup dataType="uint" bitLength="4">4</cpuFunctionGroup>
                            <cpuSubfunction dataType="uint" bitLength="8">1</cpuSubfunction>
                            <sequenceNumber dataType="uint" bitLength="8">0</sequenceNumber>
                          </S7ParameterUserDataItemCPUFunctions>
                        </S7ParameterUserDataItem>
                      </items>
                    </S7ParameterUserData>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadUserData>
                      <items isList="true">
                        <S7PayloadUserDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="OCTET_STRING">9</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">4</dataLength>
                          <S7PayloadUserDataItemCpuFunctionReadSzlRequest>
                            <szlId>
                              <SzlId>
                                <typeClass>
                                  <SzlModuleTypeClass dataType="uint" bitLength="4" stringRepresentation="CPU">0</SzlModuleTypeClass>
                                </typeClass>
                                <sublistExtract dataType="uint" bitLength="4">0</sublistExtract>
                                <sublistList>
                                  <SzlSublist dataType="uint" bitLength="8" stringRepresentation="MODULE_IDENTIFICATION">17</SzlSublist>
                                </sublistList>
                              </SzlId>
                            </szlId>
                            <szlIndex dataType="uint" bitLength="16">0</szlIndex>
                          </S7PayloadUserDataItemCpuFunctionReadSzlRequest>
                        </S7PayloadUserDataItem>
                      </items>
                    </S7PayloadUserData>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Read PLC Type Response</name>
    <raw>
      0300007d02f080320700000001000c0060000112081284010100000000ff09005c00110000001c0003000136455337203231322d31424433302d3058423020202000012020000636455337203231322d31424433302d3058423020202000012020000736455337203231322d31424433302d3058423020202056020002
    </raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">125</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">7</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">1</tpduReference>
                <parameterLength dataType="uint" bitLength="16">12</parameterLength>
                <payloadLength dataType="uint" bitLength="16">96</payloadLength>
                <S7MessageUserData>
                </S7MessageUserData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">0</parameterType>
                    <S7ParameterUserData>
                      <numItems dataType="uint" bitLength="8">1</numItems>
                      <items isList="true">
                        <S7ParameterUserDataItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7ParameterUserDataItemCPUFunctions>
                            <itemLength dataType="uint" bitLength="8">8</itemLength>
                            <method dataType="uint" bitLength="8">18</method>
                            <cpuFunctionType dataType="uint" bitLength="4">8</cpuFunctionType>
                            <cpuFunctionGroup dataType="uint" bitLength="4">4</cpuFunctionGroup>
                            <cpuSubfunction dataType="uint" bitLength="8">1</cpuSubfunction>
                            <sequenceNumber dataType="uint" bitLength="8">1</sequenceNumber>
                            <dataUnitReferenceNumber dataType="uint" bitLength="8">0</dataUnitReferenceNumber>
                            <lastDataUnit dataType="uint" bitLength="8">0</lastDataUnit>
                            <errorCode dataType="uint" bitLength="16">0</errorCode>
                          </S7ParameterUserDataItemCPUFunctions>
                        </S7ParameterUserDataItem>
                      </items>
                    </S7ParameterUserData>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadUserData>
                      <items isList="true">
                        <S7PayloadUserDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="OCTET_STRING">9</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">92</dataLength>
                          <S7PayloadUserDataItemCpuFunctionReadSzlResponse>
                            <szlId>
                              <SzlId>
                                <typeClass>
                                  <SzlModuleTypeClass dataType="uint" bitLength="4" stringRepresentation="CPU">0</SzlModuleTypeClass>
                                </typeClass>
                                <sublistExtract dataType="uint" bitLength="4">0</sublistExtract>
                                <sublistList>
                                  <SzlSublist dataType="uint" bitLength="8" stringRepresentation="MODULE_IDENTIFICATION">17</SzlSublist>
                                </sublistList>
                              </SzlId>
                            </szlId>
                            <szlIndex dataType="uint" bitLength="16">0</szlIndex>
                            <szlItemLength dataType="uint" bitLength="16">28</szlItemLength>
                            <szlItemCount dataType="uint" bitLength="16">3</szlItemCount>
                            <items isList="true">
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">1</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">1</ausbg>
                                <ausbe dataType="uint" bitLength="16">8224</ausbe>
                              </SzlDataTreeItem>
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">6</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">1</ausbg>
                                <ausbe dataType="uint" bitLength="16">8224</ausbe>
                              </SzlDataTreeItem>
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">7</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">22018</ausbg>
                                <ausbe dataType="uint" bitLength="16">2</ausbe>
                              </SzlDataTreeItem>
                            </items>
                          </S7PayloadUserDataItemCpuFunctionReadSzlResponse>
                        </S7PayloadUserDataItem>
                      </items>
                    </S7PayloadUserData>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Read Request</name>
    <raw>
      0300004302f08b32010000000b003200000404120a10010001000082000000120a10010001000082000000120a10010001000082000000120a10010001000082000000
    </raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">67</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">11</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">1</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">11</tpduReference>
                <parameterLength dataType="uint" bitLength="16">50</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageRequest>
                </S7MessageRequest>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">4</parameterType>
                    <S7ParameterReadVarRequest>
                      <numItems dataType="uint" bitLength="8">4</numItems>
                      <items isList="true">
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                      </items>
                    </S7ParameterReadVarRequest>
                  </S7Parameter>
                </parameter>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Read Response</name>
    <raw>0300002c02f08032030000000b0002001700000404ff0300010100ff0300010100ff0300010100ff03000101</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">44</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">3</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">11</tpduReference>
                <parameterLength dataType="uint" bitLength="16">2</parameterLength>
                <payloadLength dataType="uint" bitLength="16">23</payloadLength>
                <S7MessageResponseData>
                  <errorClass dataType="uint" bitLength="8">0</errorClass>
                  <errorCode dataType="uint" bitLength="8">0</errorCode>
                </S7MessageResponseData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">4</parameterType>
                    <S7ParameterReadVarResponse>
                      <numItems dataType="uint" bitLength="8">4</numItems>
                    </S7ParameterReadVarResponse>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadReadVarResponse>
                      <items isList="true">
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                          </padding>
                        </S7VarPayloadDataItem>
                      </items>
                    </S7PayloadReadVarResponse>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Read Error Response</name>
    <raw>0300001302f08032020000000a000000008500</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">19</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">2</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">10</tpduReference>
                <parameterLength dataType="uint" bitLength="16">0</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageResponse>
                  <errorClass dataType="uint" bitLength="8">133</errorClass>
                  <errorCode dataType="uint" bitLength="8">0</errorCode>
                </S7MessageResponse>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Write Request</name>
    <raw>
      0300005a02f08e32010000000e003200170504120a10010001000082000000120a10010001000082000001120a10010001000082000002120a10010001000082000003ff0300010100ff0300010100ff0300010100ff03000101
    </raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">90</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">14</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">1</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">14</tpduReference>
                <parameterLength dataType="uint" bitLength="16">50</parameterLength>
                <payloadLength dataType="uint" bitLength="16">23</payloadLength>
                <S7MessageRequest>
                </S7MessageRequest>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">5</parameterType>
                    <S7ParameterWriteVarRequest>
                      <numItems dataType="uint" bitLength="8">4</numItems>
                      <items isList="true">
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">1</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">2</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                        <S7VarRequestParameterItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7VarRequestParameterItemAddress>
                            <itemLength dataType="uint" bitLength="8">10</itemLength>
                            <address>
                              <S7Address>
                                <addressType dataType="uint" bitLength="8">16</addressType>
                                <S7AddressAny>
                                  <transportSize>
                                    <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1</TransportSize>
                                  </transportSize>
                                  <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                  <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                  <area>
                                    <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130</MemoryArea>
                                  </area>
                                  <reserved dataType="uint" bitLength="5">0</reserved>
                                  <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                  <bitAddress dataType="uint" bitLength="3">3</bitAddress>
                                </S7AddressAny>
                              </S7Address>
                            </address>
                          </S7VarRequestParameterItemAddress>
                        </S7VarRequestParameterItem>
                      </items>
                    </S7ParameterWriteVarRequest>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadWriteVarRequest>
                      <items isList="true">
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                            <value dataType="uint" bitLength="8">0</value>
                          </padding>
                        </S7VarPayloadDataItem>
                        <S7VarPayloadDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3</DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">1</dataLength>
                          <data dataType="byte" bitLength="8">0x01</data>
                          <padding isList="true">
                          </padding>
                        </S7VarPayloadDataItem>
                      </items>
                    </S7PayloadWriteVarRequest>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

  <testcase>
    <name>S7 Write Response</name>
    <raw>0300001902f08032030000000e0002000400000504ffffffff</raw>
    <root-type>TPKTPacket</root-type>
    <xml>
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">25</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">3</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">14</tpduReference>
                <parameterLength dataType="uint" bitLength="16">2</parameterLength>
                <payloadLength dataType="uint" bitLength="16">4</payloadLength>
                <S7MessageResponseData>
                  <errorClass dataType="uint" bitLength="8">0</errorClass>
                  <errorCode dataType="uint" bitLength="8">0</errorCode>
                </S7MessageResponseData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">5</parameterType>
                    <S7ParameterWriteVarResponse>
                      <numItems dataType="uint" bitLength="8">4</numItems>
                    </S7ParameterWriteVarResponse>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadWriteVarResponse>
                      <items isList="true">
                        <S7VarPayloadStatusItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                        </S7VarPayloadStatusItem>
                        <S7VarPayloadStatusItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                        </S7VarPayloadStatusItem>
                        <S7VarPayloadStatusItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                        </S7VarPayloadStatusItem>
                        <S7VarPayloadStatusItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255</DataTransportErrorCode>
                          </returnCode>
                        </S7VarPayloadStatusItem>
                      </items>
                    </S7PayloadWriteVarResponse>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </xml>
  </testcase>

</test:testsuite>