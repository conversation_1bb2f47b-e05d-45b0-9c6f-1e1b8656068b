<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:driver-testsuite xmlns:test="https://plc4x.apache.org/schemas/driver-testsuite.xsd"
                       byteOrder="BIG_ENDIAN">

  <name>S7</name>

  <protocolName>s7</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <driver-name>s7</driver-name>

  <setup>
    <!-- First the driver is expected to send a COTP connection request -->
    <outgoing-plc-message name="Send COTP Connection Request">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">22</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">17</headerLength>
            <tpduCode dataType="uint" bitLength="8">224</tpduCode>
            <COTPPacketConnectionRequest>
              <destinationReference dataType="uint" bitLength="16">0</destinationReference>
              <sourceReference dataType="uint" bitLength="16">15</sourceReference>
              <protocolClass>
                <COTPProtocolClass dataType="uint" bitLength="8" stringRepresentation="CLASS_0">0</COTPProtocolClass>
              </protocolClass>
            </COTPPacketConnectionRequest>
            <parameters isList="true">
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">193</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCallingTsap>
                  <tsapId dataType="uint" bitLength="16">785</tsapId>
                </COTPParameterCallingTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">194</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCalledTsap>
                  <tsapId dataType="uint" bitLength="16">256</tsapId>
                </COTPParameterCalledTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">192</parameterType>
                <parameterLength dataType="uint" bitLength="8">1</parameterLength>
                <COTPParameterTpduSize>
                  <tpduSize>
                    <COTPTpduSize dataType="uint" bitLength="8" stringRepresentation="SIZE_1024">10</COTPTpduSize>
                  </tpduSize>
                </COTPParameterTpduSize>
              </COTPParameter>
            </parameters>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </outgoing-plc-message>
    <!-- The PLC will send a COTP connection response -->
    <incoming-plc-message name="Receive COTP Connection Response">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">22</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">17</headerLength>
            <tpduCode dataType="uint" bitLength="8">208</tpduCode>
            <COTPPacketConnectionResponse>
              <destinationReference dataType="uint" bitLength="16">15</destinationReference>
              <sourceReference dataType="uint" bitLength="16">11</sourceReference>
              <protocolClass>
                <COTPProtocolClass dataType="uint" bitLength="8" stringRepresentation="CLASS_0">0</COTPProtocolClass>
              </protocolClass>
            </COTPPacketConnectionResponse>
            <parameters isList="true">
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">192</parameterType>
                <parameterLength dataType="uint" bitLength="8">1</parameterLength>
                <COTPParameterTpduSize>
                  <tpduSize>
                    <COTPTpduSize dataType="uint" bitLength="8" stringRepresentation="SIZE_1024">10</COTPTpduSize>
                  </tpduSize>
                </COTPParameterTpduSize>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">193</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCallingTsap>
                  <tsapId dataType="uint" bitLength="16">785</tsapId>
                </COTPParameterCallingTsap>
              </COTPParameter>
              <COTPParameter>
                <parameterType dataType="uint" bitLength="8">194</parameterType>
                <parameterLength dataType="uint" bitLength="8">2</parameterLength>
                <COTPParameterCalledTsap>
                  <tsapId dataType="uint" bitLength="16">256</tsapId>
                </COTPParameterCalledTsap>
              </COTPParameter>
            </parameters>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </incoming-plc-message>
    <!-- After that the driver will send a S7 connection request -->
    <outgoing-plc-message name="Send S7 Connection Request">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">25</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">1</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">1</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">0</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageRequest>
                </S7MessageRequest>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">240</parameterType>
                    <S7ParameterSetupCommunication>
                      <reserved dataType="uint" bitLength="8">0</reserved>
                      <maxAmqCaller dataType="uint" bitLength="16">8</maxAmqCaller>
                      <maxAmqCallee dataType="uint" bitLength="16">8</maxAmqCallee>
                      <pduLength dataType="uint" bitLength="16">1008</pduLength>
                    </S7ParameterSetupCommunication>
                  </S7Parameter>
                </parameter>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </outgoing-plc-message>
    <!-- The PLC will send a S7 connection response -->
    <incoming-plc-message name="Receive S7 Connection Response">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">27</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">3</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">0</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                <S7MessageResponseData>
                  <errorClass dataType="uint" bitLength="8">0</errorClass>
                  <errorCode dataType="uint" bitLength="8">0</errorCode>
                </S7MessageResponseData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">240</parameterType>
                    <S7ParameterSetupCommunication>
                      <reserved dataType="uint" bitLength="8">0</reserved>
                      <maxAmqCaller dataType="uint" bitLength="16">3</maxAmqCaller>
                      <maxAmqCallee dataType="uint" bitLength="16">3</maxAmqCallee>
                      <pduLength dataType="uint" bitLength="16">240</pduLength>
                    </S7ParameterSetupCommunication>
                  </S7Parameter>
                </parameter>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>

    </incoming-plc-message>
    <!-- Next we'll query some type information -->
    <outgoing-plc-message name="Send S7 Identification Request">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">33</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">2</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">7</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">1</tpduReference>
                <parameterLength dataType="uint" bitLength="16">8</parameterLength>
                <payloadLength dataType="uint" bitLength="16">8</payloadLength>
                <S7MessageUserData>
                </S7MessageUserData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">0</parameterType>
                    <S7ParameterUserData>
                      <numItems dataType="uint" bitLength="8">1</numItems>
                      <items isList="true">
                        <S7ParameterUserDataItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7ParameterUserDataItemCPUFunctions>
                            <itemLength dataType="uint" bitLength="8">4</itemLength>
                            <method dataType="uint" bitLength="8">17</method>
                            <cpuFunctionType dataType="uint" bitLength="4">4</cpuFunctionType>
                            <cpuFunctionGroup dataType="uint" bitLength="4">4</cpuFunctionGroup>
                            <cpuSubfunction dataType="uint" bitLength="8">1</cpuSubfunction>
                            <sequenceNumber dataType="uint" bitLength="8">0</sequenceNumber>
                          </S7ParameterUserDataItemCPUFunctions>
                        </S7ParameterUserDataItem>
                      </items>
                    </S7ParameterUserData>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadUserData>
                      <items isList="true">
                        <S7PayloadUserDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255
                            </DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="OCTET_STRING">9
                            </DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">4</dataLength>
                          <S7PayloadUserDataItemCpuFunctionReadSzlRequest>
                            <szlId>
                              <SzlId>
                                <typeClass>
                                  <SzlModuleTypeClass dataType="uint" bitLength="4" stringRepresentation="CPU">0
                                  </SzlModuleTypeClass>
                                </typeClass>
                                <sublistExtract dataType="uint" bitLength="4">0</sublistExtract>
                                <sublistList>
                                  <SzlSublist dataType="uint" bitLength="8"
                                              stringRepresentation="MODULE_IDENTIFICATION">17
                                  </SzlSublist>
                                </sublistList>
                              </SzlId>
                            </szlId>
                            <szlIndex dataType="uint" bitLength="16">0</szlIndex>
                          </S7PayloadUserDataItemCpuFunctionReadSzlRequest>
                        </S7PayloadUserDataItem>
                      </items>
                    </S7PayloadUserData>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </outgoing-plc-message>
    <!-- Which the PLC will gladly provide to us -->
    <incoming-plc-message name="Receive S7 Identification Response">
      <TPKTPacket>
        <protocolId dataType="uint" bitLength="8">3</protocolId>
        <reserved dataType="uint" bitLength="8">0</reserved>
        <len dataType="uint" bitLength="16">125</len>
        <payload>
          <COTPPacket>
            <headerLength dataType="uint" bitLength="8">2</headerLength>
            <tpduCode dataType="uint" bitLength="8">240</tpduCode>
            <COTPPacketData>
              <eot dataType="bit" bitLength="1">true</eot>
              <tpduRef dataType="uint" bitLength="7">0</tpduRef>
            </COTPPacketData>
            <parameters isList="true">
            </parameters>
            <payload>
              <S7Message>
                <protocolId dataType="uint" bitLength="8">50</protocolId>
                <messageType dataType="uint" bitLength="8">7</messageType>
                <reserved dataType="uint" bitLength="16">0</reserved>
                <tpduReference dataType="uint" bitLength="16">1</tpduReference>
                <parameterLength dataType="uint" bitLength="16">12</parameterLength>
                <payloadLength dataType="uint" bitLength="16">96</payloadLength>
                <S7MessageUserData>
                </S7MessageUserData>
                <parameter>
                  <S7Parameter>
                    <parameterType dataType="uint" bitLength="8">0</parameterType>
                    <S7ParameterUserData>
                      <numItems dataType="uint" bitLength="8">1</numItems>
                      <items isList="true">
                        <S7ParameterUserDataItem>
                          <itemType dataType="uint" bitLength="8">18</itemType>
                          <S7ParameterUserDataItemCPUFunctions>
                            <itemLength dataType="uint" bitLength="8">8</itemLength>
                            <method dataType="uint" bitLength="8">18</method>
                            <cpuFunctionType dataType="uint" bitLength="4">8</cpuFunctionType>
                            <cpuFunctionGroup dataType="uint" bitLength="4">4</cpuFunctionGroup>
                            <cpuSubfunction dataType="uint" bitLength="8">1</cpuSubfunction>
                            <sequenceNumber dataType="uint" bitLength="8">1</sequenceNumber>
                            <dataUnitReferenceNumber dataType="uint" bitLength="8">0</dataUnitReferenceNumber>
                            <lastDataUnit dataType="uint" bitLength="8">0</lastDataUnit>
                            <errorCode dataType="uint" bitLength="16">0</errorCode>
                          </S7ParameterUserDataItemCPUFunctions>
                        </S7ParameterUserDataItem>
                      </items>
                    </S7ParameterUserData>
                  </S7Parameter>
                </parameter>
                <payload>
                  <S7Payload>
                    <S7PayloadUserData>
                      <items isList="true">
                        <S7PayloadUserDataItem>
                          <returnCode>
                            <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255
                            </DataTransportErrorCode>
                          </returnCode>
                          <transportSize>
                            <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="OCTET_STRING">9
                            </DataTransportSize>
                          </transportSize>
                          <dataLength dataType="uint" bitLength="16">92</dataLength>
                          <S7PayloadUserDataItemCpuFunctionReadSzlResponse>
                            <szlId>
                              <SzlId>
                                <typeClass>
                                  <SzlModuleTypeClass dataType="uint" bitLength="4" stringRepresentation="CPU">0
                                  </SzlModuleTypeClass>
                                </typeClass>
                                <sublistExtract dataType="uint" bitLength="4">0</sublistExtract>
                                <sublistList>
                                  <SzlSublist dataType="uint" bitLength="8"
                                              stringRepresentation="MODULE_IDENTIFICATION">17
                                  </SzlSublist>
                                </sublistList>
                              </SzlId>
                            </szlId>
                            <szlIndex dataType="uint" bitLength="16">0</szlIndex>
                            <szlItemLength dataType="uint" bitLength="16">28</szlItemLength>
                            <szlItemCount dataType="uint" bitLength="16">3</szlItemCount>
                            <items isList="true">
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">1</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">1</ausbg>
                                <ausbe dataType="uint" bitLength="16">8224</ausbe>
                              </SzlDataTreeItem>
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">6</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">1</ausbg>
                                <ausbe dataType="uint" bitLength="16">8224</ausbe>
                              </SzlDataTreeItem>
                              <SzlDataTreeItem>
                                <itemIndex dataType="uint" bitLength="16">7</itemIndex>
                                <mlfb dataType="byte" bitLength="160">0x36455337203231322d31424433302d3058423020</mlfb>
                                <moduleTypeId dataType="uint" bitLength="16">8224</moduleTypeId>
                                <ausbg dataType="uint" bitLength="16">22018</ausbg>
                                <ausbe dataType="uint" bitLength="16">2</ausbe>
                              </SzlDataTreeItem>
                            </items>
                          </S7PayloadUserDataItemCpuFunctionReadSzlResponse>
                        </S7PayloadUserDataItem>
                      </items>
                    </S7PayloadUserData>
                  </S7Payload>
                </payload>
              </S7Message>
            </payload>
          </COTPPacket>
        </payload>
      </TPKTPacket>
    </incoming-plc-message>
  </setup>

  <testcase>
    <name>Single element read request</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
            <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
               <name>hurz</name>
               <address>%Q0.0:BOOL</address>
            </tag>
            </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send S7 Read Request">
        <TPKTPacket>
          <protocolId dataType="uint" bitLength="8">3</protocolId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <len dataType="uint" bitLength="16">31</len>
          <payload>
            <COTPPacket>
              <headerLength dataType="uint" bitLength="8">2</headerLength>
              <tpduCode dataType="uint" bitLength="8">240</tpduCode>
              <COTPPacketData>
                <eot dataType="bit" bitLength="1">true</eot>
                <tpduRef dataType="uint" bitLength="7">10</tpduRef>
              </COTPPacketData>
              <parameters isList="true">
              </parameters>
              <payload>
                <S7Message>
                  <protocolId dataType="uint" bitLength="8">50</protocolId>
                  <messageType dataType="uint" bitLength="8">1</messageType>
                  <reserved dataType="uint" bitLength="16">0</reserved>
                  <tpduReference dataType="uint" bitLength="16">10</tpduReference>
                  <parameterLength dataType="uint" bitLength="16">14</parameterLength>
                  <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                  <S7MessageRequest>
                  </S7MessageRequest>
                  <parameter>
                    <S7Parameter>
                      <parameterType dataType="uint" bitLength="8">4</parameterType>
                      <S7ParameterReadVarRequest>
                        <numItems dataType="uint" bitLength="8">1</numItems>
                        <items isList="true">
                          <S7VarRequestParameterItem>
                            <itemType dataType="uint" bitLength="8">18</itemType>
                            <S7VarRequestParameterItemAddress>
                              <itemLength dataType="uint" bitLength="8">10</itemLength>
                              <address>
                                <S7Address>
                                  <addressType dataType="uint" bitLength="8">16</addressType>
                                  <S7AddressAny>
                                    <transportSize>
                                      <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1
                                      </TransportSize>
                                    </transportSize>
                                    <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                    <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                    <area>
                                      <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130
                                      </MemoryArea>
                                    </area>
                                    <reserved dataType="uint" bitLength="5">0</reserved>
                                    <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                    <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                  </S7AddressAny>
                                </S7Address>
                              </address>
                            </S7VarRequestParameterItemAddress>
                          </S7VarRequestParameterItem>
                        </items>
                      </S7ParameterReadVarRequest>
                    </S7Parameter>
                  </parameter>
                </S7Message>
              </payload>
            </COTPPacket>
          </payload>
        </TPKTPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive S7 Read Response">
        <TPKTPacket>
          <protocolId dataType="uint" bitLength="8">3</protocolId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <len dataType="uint" bitLength="16">26</len>
          <payload>
            <COTPPacket>
              <headerLength dataType="uint" bitLength="8">2</headerLength>
              <tpduCode dataType="uint" bitLength="8">240</tpduCode>
              <COTPPacketData>
                <eot dataType="bit" bitLength="1">true</eot>
                <tpduRef dataType="uint" bitLength="7">0</tpduRef>
              </COTPPacketData>
              <parameters isList="true">
              </parameters>
              <payload>
                <S7Message>
                  <protocolId dataType="uint" bitLength="8">50</protocolId>
                  <messageType dataType="uint" bitLength="8">3</messageType>
                  <reserved dataType="uint" bitLength="16">0</reserved>
                  <tpduReference dataType="uint" bitLength="16">10</tpduReference>
                  <parameterLength dataType="uint" bitLength="16">2</parameterLength>
                  <payloadLength dataType="uint" bitLength="16">6</payloadLength>
                  <S7MessageResponseData>
                    <errorClass dataType="uint" bitLength="8">0</errorClass>
                    <errorCode dataType="uint" bitLength="8">0</errorCode>
                  </S7MessageResponseData>
                  <parameter>
                    <S7Parameter>
                      <parameterType dataType="uint" bitLength="8">4</parameterType>
                      <S7ParameterReadVarResponse>
                        <numItems dataType="uint" bitLength="8">1</numItems>
                      </S7ParameterReadVarResponse>
                    </S7Parameter>
                  </parameter>
                  <payload>
                    <S7Payload>
                      <S7PayloadReadVarResponse>
                        <items isList="true">
                          <S7VarPayloadDataItem>
                            <returnCode>
                              <DataTransportErrorCode dataType="uint" bitLength="8" stringRepresentation="OK">255
                              </DataTransportErrorCode>
                            </returnCode>
                            <transportSize>
                              <DataTransportSize dataType="uint" bitLength="8" stringRepresentation="BIT">3
                              </DataTransportSize>
                            </transportSize>
                            <dataLength dataType="uint" bitLength="16">1</dataLength>
                            <data dataType="byte" bitLength="8">0x01</data>
                            <padding isList="true">
                            </padding>
                          </S7VarPayloadDataItem>
                        </items>
                      </S7PayloadReadVarResponse>
                    </S7Payload>
                  </payload>
                </S7Message>
              </payload>
            </COTPPacket>
          </payload>
        </TPKTPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <S7Tag>
                      <memoryArea dataType="string" bitLength="56" encoding="UTF-8">OUTPUTS</memoryArea>
                      <blockNumber dataType="uint" bitLength="16">0</blockNumber>
                      <byteOffset dataType="uint" bitLength="16">0</byteOffset>
                      <bitOffset dataType="uint" bitLength="8">0</bitOffset>
                      <numElements dataType="uint" bitLength="16">1</numElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                    </S7Tag>
                  </hurz>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcBOOL dataType="bit" bitLength="1">true</PlcBOOL>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Single element read request with disabled PUT/GET</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz</name>
              <address>%Q0.0:BOOL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send S7 Read Request">
        <TPKTPacket>
          <protocolId dataType="uint" bitLength="8">3</protocolId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <len dataType="uint" bitLength="16">31</len>
          <payload>
            <COTPPacket>
              <headerLength dataType="uint" bitLength="8">2</headerLength>
              <tpduCode dataType="uint" bitLength="8">240</tpduCode>
              <COTPPacketData>
                <eot dataType="bit" bitLength="1">true</eot>
                <tpduRef dataType="uint" bitLength="7">10</tpduRef>
              </COTPPacketData>
              <parameters isList="true">
              </parameters>
              <payload>
                <S7Message>
                  <protocolId dataType="uint" bitLength="8">50</protocolId>
                  <messageType dataType="uint" bitLength="8">1</messageType>
                  <reserved dataType="uint" bitLength="16">0</reserved>
                  <tpduReference dataType="uint" bitLength="16">10</tpduReference>
                  <parameterLength dataType="uint" bitLength="16">14</parameterLength>
                  <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                  <S7MessageRequest>
                  </S7MessageRequest>
                  <parameter>
                    <S7Parameter>
                      <parameterType dataType="uint" bitLength="8">4</parameterType>
                      <S7ParameterReadVarRequest>
                        <numItems dataType="uint" bitLength="8">1</numItems>
                        <items isList="true">
                          <S7VarRequestParameterItem>
                            <itemType dataType="uint" bitLength="8">18</itemType>
                            <S7VarRequestParameterItemAddress>
                              <itemLength dataType="uint" bitLength="8">10</itemLength>
                              <address>
                                <S7Address>
                                  <addressType dataType="uint" bitLength="8">16</addressType>
                                  <S7AddressAny>
                                    <transportSize>
                                      <TransportSize dataType="uint" bitLength="8" stringRepresentation="BOOL">1
                                      </TransportSize>
                                    </transportSize>
                                    <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                                    <dbNumber dataType="uint" bitLength="16">0</dbNumber>
                                    <area>
                                      <MemoryArea dataType="uint" bitLength="8" stringRepresentation="OUTPUTS">130
                                      </MemoryArea>
                                    </area>
                                    <reserved dataType="uint" bitLength="5">0</reserved>
                                    <byteAddress dataType="uint" bitLength="16">0</byteAddress>
                                    <bitAddress dataType="uint" bitLength="3">0</bitAddress>
                                  </S7AddressAny>
                                </S7Address>
                              </address>
                            </S7VarRequestParameterItemAddress>
                          </S7VarRequestParameterItem>
                        </items>
                      </S7ParameterReadVarRequest>
                    </S7Parameter>
                  </parameter>
                </S7Message>
              </payload>
            </COTPPacket>
          </payload>
        </TPKTPacket>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive S7 Read Response">
        <TPKTPacket>
          <protocolId dataType="uint" bitLength="8">3</protocolId>
          <reserved dataType="uint" bitLength="8">0</reserved>
          <len dataType="uint" bitLength="16">19</len>
          <payload>
            <COTPPacket>
              <headerLength dataType="uint" bitLength="8">2</headerLength>
              <tpduCode dataType="uint" bitLength="8">240</tpduCode>
              <COTPPacketData>
                <eot dataType="bit" bitLength="1">true</eot>
                <tpduRef dataType="uint" bitLength="7">0</tpduRef>
              </COTPPacketData>
              <parameters isList="true">
              </parameters>
              <payload>
                <S7Message>
                  <protocolId dataType="uint" bitLength="8">50</protocolId>
                  <messageType dataType="uint" bitLength="8">2</messageType>
                  <reserved dataType="uint" bitLength="16">0</reserved>
                  <tpduReference dataType="uint" bitLength="16">10</tpduReference>
                  <parameterLength dataType="uint" bitLength="16">0</parameterLength>
                  <payloadLength dataType="uint" bitLength="16">0</payloadLength>
                  <S7MessageResponse>
                    <errorClass dataType="uint" bitLength="8">129</errorClass>
                    <errorCode dataType="uint" bitLength="8">4</errorCode>
                  </S7MessageResponse>
                </S7Message>
              </payload>
            </COTPPacket>
          </payload>
        </TPKTPacket>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <S7Tag>
                      <memoryArea dataType="string" bitLength="56" encoding="UTF-8">OUTPUTS</memoryArea>
                      <blockNumber dataType="uint" bitLength="16">0</blockNumber>
                      <byteOffset dataType="uint" bitLength="16">0</byteOffset>
                      <bitOffset dataType="uint" bitLength="8">0</bitOffset>
                      <numElements dataType="uint" bitLength="16">1</numElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">BOOL</dataType>
                    </S7Tag>
                  </hurz>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="104" encoding="UTF-8">ACCESS_DENIED</code>
                <value>
                  <PlcNull></PlcNull>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

</test:driver-testsuite>
