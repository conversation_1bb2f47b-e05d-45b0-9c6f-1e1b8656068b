<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
  -->
<test:driver-testsuite xmlns:test="https://plc4x.apache.org/schemas/driver-testsuite.xsd"
                       byteOrder="BIG_ENDIAN">

  <!-- https://base64.guru/converter/encode/hex -->

  <name>Modbus</name>

  <protocolName>modbus</protocolName>
  <outputFlavor>read-write</outputFlavor>

  <driver-name>modbus-tcp</driver-name>

  <testcase>
    <name>Single element read request</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz</name>
              <address>holding-register:1:REAL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Modbus Input-Register Read Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">2</quantity>
                </ModbusPDUReadHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Modbus Input-Register Read Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">7</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersResponse>
                  <byteCount dataType="uint" bitLength="8">4</byteCount>
                  <value dataType="byte" bitLength="32">0x40490fdb</value>
                </ModbusPDUReadHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">0</address>
                      <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Array element read request</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz</name>
              <address>holding-register:1:REAL[2]</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send Modbus Input-Register Read Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">4</quantity>
                </ModbusPDUReadHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Modbus Input-Register Read Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">11</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersResponse>
                  <byteCount dataType="uint" bitLength="8">8</byteCount>
                  <value dataType="byte" bitLength="64">0x40490fdb40490fdb</value>
                </ModbusPDUReadHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">0</address>
                      <numberOfElements dataType="uint" bitLength="16">2</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcList>
                    <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                    <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                  </PlcList>
                </value>
              </PlcResponseItem>
            </hurz>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Multi element read request</name>
    <steps>
      <api-request name="Receive Read Request from application">
        <TestReadRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz1</name>
              <address>holding-register:1:REAL</address>
            </tag>
            <tag className="org.apache.plc4x.test.driver.internal.api.TestTag">
              <name>hurz2</name>
              <address>holding-register:3:REAL</address>
            </tag>
          </tags>
        </TestReadRequest>
      </api-request>
      <outgoing-plc-message name="Send First Item Modbus Input-Register Read Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">2</quantity>
                </ModbusPDUReadHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive First Item Modbus Input-Register Read Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">7</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersResponse>
                  <byteCount dataType="uint" bitLength="8">4</byteCount>
                  <value dataType="byte" bitLength="32">0x40490fdb</value>
                </ModbusPDUReadHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <outgoing-plc-message name="Send Second Item Modbus Input-Register Read Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">2</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">2</startingAddress>
                  <quantity dataType="uint" bitLength="16">2</quantity>
                </ModbusPDUReadHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Second Item Modbus Input-Register Read Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">2</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">7</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">3</functionFlag>
                <ModbusPDUReadHoldingRegistersResponse>
                  <byteCount dataType="uint" bitLength="8">4</byteCount>
                  <value dataType="byte" bitLength="32">0x40490fdb</value>
                </ModbusPDUReadHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <api-response name="Report Read Response to application">
        <PlcReadResponse>
          <request>
            <PlcReadRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz1>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">0</address>
                      <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz1>
                  <hurz2>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">2</address>
                      <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz2>
                </tags>
              </PlcTagRequest>
            </PlcReadRequest>
          </request>
          <values isList="true">
            <hurz1>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                </value>
              </PlcResponseItem>
            </hurz1>
            <hurz2>
              <PlcResponseItem>
                <code dataType="string" bitLength="16" encoding="UTF-8">OK</code>
                <value>
                  <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                </value>
              </PlcResponseItem>
            </hurz2>
          </values>
        </PlcReadResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Single element write request</name>
    <steps>
      <api-request name="Receive Write Request from application">
        <TestWriteRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestValueTag">
              <name>hurz</name>
              <address>holding-register:1:REAL</address>
              <value>3.1415927</value>
            </tag>
          </tags>
        </TestWriteRequest>
      </api-request>
      <outgoing-plc-message name="Send Modbus Input-Register Write Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">11</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">16</functionFlag>
                <ModbusPDUWriteMultipleHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">2</quantity>
                  <byteCount dataType="uint" bitLength="8">4</byteCount>
                  <value dataType="byte" bitLength="32">0x40490fdb</value>
                </ModbusPDUWriteMultipleHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Modbus Input-Register Write Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">16</functionFlag>
                <ModbusPDUWriteMultipleHoldingRegistersResponse>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">2</quantity>
                </ModbusPDUWriteMultipleHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <api-response name="Report Write Response to application">
        <PlcWriteResponse>
          <request>
            <PlcWriteRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">0</address>
                      <numberOfElements dataType="uint" bitLength="16">1</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz>
                </tags>
              </PlcTagRequest>
              <values isList="true">
                <hurz>
                  <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                </hurz>
              </values>
            </PlcWriteRequest>
          </request>
          <responseCodes isList="true">
            <hurz>
              <ResponseCode dataType="uint" bitLength="8" stringRepresentation="OK">1</ResponseCode>
            </hurz>
          </responseCodes>
        </PlcWriteResponse>
      </api-response>
    </steps>
  </testcase>

  <testcase>
    <name>Array element write request</name>
    <steps>
      <api-request name="Receive Write Request from application">
        <TestWriteRequest>
          <tags isList="true">
            <tag className="org.apache.plc4x.test.driver.internal.api.TestValueTag">
              <name>hurz</name>
              <address>holding-register:1:REAL[2]</address>
              <value>3.1415927</value>
              <value>3.1415927</value>
            </tag>
          </tags>
        </TestWriteRequest>
      </api-request>
      <outgoing-plc-message name="Send Modbus Input-Register Write Request">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>false</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">15</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">16</functionFlag>
                <ModbusPDUWriteMultipleHoldingRegistersRequest>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">4</quantity>
                  <byteCount dataType="uint" bitLength="8">8</byteCount>
                  <value dataType="byte" bitLength="64">0x40490fdb40490fdb</value>
                </ModbusPDUWriteMultipleHoldingRegistersRequest>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </outgoing-plc-message>
      <incoming-plc-message name="Receive Modbus Input-Register Write Response">
        <parser-arguments>
          <driverType>MODBUS_TCP</driverType>
          <response>true</response>
        </parser-arguments>
        <ModbusADU>
          <ModbusTcpADU>
            <transactionIdentifier dataType="uint" bitLength="16">1</transactionIdentifier>
            <protocolIdentifier dataType="uint" bitLength="16">0</protocolIdentifier>
            <length dataType="uint" bitLength="16">6</length>
            <unitIdentifier dataType="uint" bitLength="8">1</unitIdentifier>
            <pdu>
              <ModbusPDU>
                <errorFlag dataType="bit" bitLength="1">false</errorFlag>
                <functionFlag dataType="uint" bitLength="7">16</functionFlag>
                <ModbusPDUWriteMultipleHoldingRegistersResponse>
                  <startingAddress dataType="uint" bitLength="16">0</startingAddress>
                  <quantity dataType="uint" bitLength="16">4</quantity>
                </ModbusPDUWriteMultipleHoldingRegistersResponse>
              </ModbusPDU>
            </pdu>
          </ModbusTcpADU>
        </ModbusADU>
      </incoming-plc-message>
      <api-response name="Report Write Response to application">
        <PlcWriteResponse>
          <request>
            <PlcWriteRequest>
              <PlcTagRequest>
                <tags isList="true">
                  <hurz>
                    <ModbusTagHoldingRegister>
                      <address dataType="uint" bitLength="16">0</address>
                      <numberOfElements dataType="uint" bitLength="16">2</numberOfElements>
                      <dataType dataType="string" bitLength="32" encoding="UTF-8">REAL</dataType>
                    </ModbusTagHoldingRegister>
                  </hurz>
                </tags>
              </PlcTagRequest>
              <values isList="true">
                <hurz>
                  <PlcList>
                    <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                    <PlcREAL dataType="float" bitLength="32">3.1415927410125732</PlcREAL>
                  </PlcList>
                </hurz>
              </values>
            </PlcWriteRequest>
          </request>
          <responseCodes isList="true">
            <hurz>
              <ResponseCode dataType="uint" bitLength="8" stringRepresentation="OK">1</ResponseCode>
            </hurz>
          </responseCodes>
        </PlcWriteResponse>
      </api-response>
    </steps>
  </testcase>

</test:driver-testsuite>
